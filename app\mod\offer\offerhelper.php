<?php if (!defined('DS')) {
    exit();
}


class offerhelper
{


    public static function db_init()
    {

        $createDataBaseSql = "
        CREATE DATABASE IF NOT EXISTS offerpdf
        CHARACTER SET utf8mb4
        COLLATE utf8mb4_0900_ai_ci;
        ";

        $dbInit = core::sql()->query($createDataBaseSql);

        if ($dbInit === false) {
            return [
                "success" => false,
                "msg" => "Database creation error"
            ];
        }

        $createMirationTableSql = "
        CREATE TABLE IF NOT EXISTS `migrations` (
        `id` int NOT NULL AUTO_INCREMENT PRIMARY KEY,
        `migration_id` int NOT NULL,
        `migration_name` text NOT NULL,
        `migration_date` timestamp NOT NULL,
        `migration_user` text NOT NULL
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
        ";

        core::sql()->query("USE offerpdf;");
        $migrationTable = core::sql()->query($createMirationTableSql);

        if ($migrationTable === false) {
            return [
                "success" => false,
                "msg" => "Migrations table creation error"
            ];
        }

        return [
            "success" => true,
            "msg" => "Database initiation successful"
        ];
    }

    public static function executeDatabaseQuery($query)
    {


        $result = core::sql()->query($query);

        if ($result === false) {
            $error = [
                'success' => false,
                'error' => "Błąd zapytania: " . core::sql()->error,
                'query' => $query
            ];
            return $error;
        }

        if ($result instanceof mysqli_result) {
            $data = $result->fetch_all(MYSQLI_ASSOC);
            $rowCount = $result->num_rows;
            // $result->free();
        } else {
            $data = [core::sql()->affected_rows];
            $rowCount = core::sql()->affected_rows;
        }

        return [
            'success' => true,
            'data' => $rowCount === 1 ? $data[0] : $data,
            'row_count' => $rowCount,
        ];
    }

    public static function executeDatabaseQueryWithParams($query, $params = [])
    {
        $mysqli = core::sql(); // Zakładam, że ta funkcja zwraca obiekt mysqli

        if (empty($params)) {
            // Standardowe zapytanie bez parametrów
            $result = $mysqli->query($query);

            if ($result === false) {
                $error = [
                    'success' => false,
                    'error' => "Błąd zapytania: " . $mysqli->error,
                    'query' => $query
                ];
                return $error;
            }

            if ($result instanceof mysqli_result) {
                $data = $result->fetch_all(MYSQLI_ASSOC);
                $rowCount = $result->num_rows;
                // $result->free();
            } else {
                $data = [$mysqli->affected_rows];
                $rowCount = $mysqli->affected_rows;
            }
        } else {
            // Zapytanie parametryzowane
            $stmt = $mysqli->prepare($query);

            if ($stmt === false) {
                $error = [
                    'success' => false,
                    'error' => "Błąd przygotowania zapytania: " . $mysqli->error,
                    'query' => $query
                ];
                return $error;
            }

            // Przygotuj typy parametrów i wartości dla bind_param
            $types = '';
            $bindParams = [];

            foreach ($params as $param) {
                if (is_int($param)) {
                    $types .= 'i'; // integer
                } elseif (is_float($param)) {
                    $types .= 'd'; // double/float
                } elseif (is_string($param)) {
                    $types .= 's'; // string
                } else {
                    $types .= 'b'; // blob
                }
                $bindParams[] = $param;
            }

            // Dodaj typ na początek tablicy parametrów (wymagane przez bind_param)
            array_unshift($bindParams, $types);

            // Użyj call_user_func_array do wywołania bind_param z tablicą parametrów
            if (!empty($params)) {
                @call_user_func_array([$stmt, 'bind_param'], $bindParams);
            }

            // Wykonaj zapytanie
            $stmt->execute();

            if ($stmt->error) {
                $error = [
                    'success' => false,
                    'error' => "Błąd wykonania zapytania: " . $stmt->error,
                    'query' => $query
                ];
                $stmt->close();
                return $error;
            }

            $result = $stmt->get_result();

            if ($result) {
                // Zapytanie SELECT
                $data = $result->fetch_all(MYSQLI_ASSOC);
                $rowCount = $result->num_rows;
            } else {
                // Zapytanie INSERT, UPDATE, DELETE
                $data = [$stmt->affected_rows];
                $rowCount = $stmt->affected_rows;
            }

            $stmt->close();
        }

        return [
            'success' => true,
            'data' => $rowCount === 1 ? $data[0] : $data,
            'row_count' => $rowCount,
        ];
    }


    public static function getFieldSeed()
    {
        return [
            "fields" => [
                "client" => "Company XYZ",
                "project" => "HCl Tank DN3000",
                "yourReference" => "1--------1",
                "ourReference" => "25700000",

                "clientName" => "TEST DEMO",
                "clientPhone" => "************",
                "clientEmail" => "<EMAIL>",
                "clientAddress" => "123 Main Street",
                "clientCity" => "New York",
                "clientPostCode" => "10001",
                "clientCountry" => "United States",

                "salesName" => "John Doe",
                "salesEmail" => "<EMAIL>",
                "salesPhone" => "************",

                "technicalName" => "Jane Doe",
                "technicalEmail" => "<EMAIL>",
                "technicalPhone" => "************",

                "date" => "2022-01-01",
                "city" => "Toruń",

                'coverLetter' => "
                <p>In response to your inquiry, we have prepared the following proposal. We kindly request you to carefully review this document to ensure that all the requested elements have been included within our scope. </p>
                <br>
                <p>We are confident that our offer will capture your interest. Nevertheless, if you have any questions, require further information, or believe that we may have overlooked anything, please feel free to reach out to us. </p>
                <br>
                <p>We are here to assist you promptly.</p>
                ",


                "pricingIncoterms" => "FCA Toruń",
                "pricingTable" => [],
                "pricingTotal" => '15 301,00',
                "pricingTotalQty" => 1,
                "pricingTotalCurrency" => '€',

                'documentationLanguage' => "English",
                'documentationCopies' => 1,
                'documentationAccording' => "EN10204 - 3.1 (not for components from sub-suppliers for which a 2.2 certificate is provided)",
                'documentationIncluding' => [
                    "Conformity certificate",
                    "Warranty",
                    "Material Certificates",
                    "Strength Calculation (if mentioned above)",
                    "Company Qualifications",
                    "Staff Qualifications",
                    "Welding/Lamination review",
                    "Barcol Hardness test",
                    "Thicknesses check",
                    "Main dimensions check",

                ],

                'conditionsTerms' => [
                    "Invoicing" => [
                        "40% at order confirmation",
                        "55% monthly progress payment",
                        "5% after documentation"
                    ],
                    "Payment" => [
                        "30 days net from date of invoice",
                    ],
                    "Prices" => [
                        "All prices excluding VAT / withholding tax"
                    ],
                    "Warranty" => ["24 months warranty after commissioning", " max. 36 months after delivery"],
                    "Validity quotation" => ["10 days"],

                ],
                "conditionsNotes" => "However, even if the offer is accepted within the aforementioned period and without prejudice to further rights, we are entitled to increase the price unilaterally in relation to the increase in price-forming factors, as exemplified below, provided that the agreed delivery date is postponed at your request or for other reasons not falling within our sphere of responsibility, and, in the period between acceptance of the offer and the time of delivery, price-forming factors, in particular commodity prices, wage costs and ancillary wage costs, procurement costs and/or production costs increase. Other price adjustment options, in particular within our General Terms and Conditions, remain unaffected unless this is in contradiction to the above regulation.",


                'conditionsSlidingPrice' => "
          
            <p>
            Due to the dynamic development on the raw material and logistics market (shortage and therefore related cost development), we see ourselves forced to forward cost increases, especially for raw materials and transports, to our clients. 
            </p>
            <p>
            If these prices increase at the time of execution compared to the day of our offer by more than 5%, the invoicing price will be billed acc. to the following sliding price formula:  P = P0 * (a + r*Rm/R0) whereby:
                </p>
            <p>
           <br>
P0 = 	price at the time of order <br>
P= 	price at the time of invoicing (original scope of supply)<br>
r= 	proportion of cost for raw materials and transports<br>
a= 	proportion of remaining costs (except mentioned under “r”) with no price sliding<br>
(a + r =1)<br>
<br>
Rm= 	raw material and transport price at the time of adjustment <br>
R0= 	raw material and transport price at the time of order <br>

<br>
In the event of a change request, we will provide the verification of the adjustment by submitting a calculation.
<br>
In the case of large-scale projects or successive supply contracts in which the production extends over a period of 6 months or longer, the above rule will apply again using the aforementioned formula if the raw material and/or transport price increases again even after a previous adjustment has already been made.

            </p>",
                'conditionsSaleAndService' => "
            <p>
            The current version of the conditions of sale and delivery applies exclusively, you can call them up on the Internet at https://www.plasticoncomposites.com/files/legal/2020-terms-and-conditions.pdf. On request, we can also send them to you free of charge. In the event of a contradiction to the arrangements made in this offer, the content of the offer prevails.
            </p>",

                'conditionsLimitationLiability' => "
            <p>
Our liability is limited to the net price of the respective order. Indirect damage such as loss of production, loss of profit etc. and consequential damage, i.e. damage to other items of the buyer, are excluded. 
            </p>

            <p>
                The above restrictions/exclusions do not apply in the event of a violation of material contractual obligations (e.g. an obligation whose performance characterizes the contract and on which the buyer can rely), in the event of damage caused intentionally or by gross negligence, in the event of injury to life, limb or health or if there arise claims under the Product Liability Act or in any other cases of mandatory legal liability. 
            </p>

            <p>
                Other limitations of liability, in particular within our General Terms and Conditions, remain unaffected.
            </p>

            <p style='font-weight: bold; color: red;'>
Arbitration / Applicable law  [Restrict use to contracting parties outside Europe without an enforcement agreement or limited enforcement options, such as China, USA, Russia]
            </p>
            <p>
                All disputes of any kind between the parties arising from this contract or in connection with its performance, including those regarding the validity of this contract and this arbitration clause, shall be finally arbitrated without recourse to ordinary courts of law in accordance with the rules of arbitration of the German Arbitration Institute (Deutsche Institution für Schiedsgerichtsbarkeit e.V. (DIS)). The arbitral tribunal shall comprise of three arbitrators whereby each party shall appoint one arbitrator and the two arbitrators appointed by the parties shall jointly appoint an umpire as third arbitrator. An award rendered can be declared enforceable by the competent public court upon application by a party. The award of the arbitral tribunal is without appeal. The award should also include a decision on the costs of the proceedings, including the arbitrators’ remuneration. Place of arbitration shall be Düsseldorf, Federal Republic of Germany. Arbitration proceedings shall be conducted in English. German law shall apply to the exclusion of the United Nations Convention on Contracts for the International Sale of Goods (CISG).
            </p>
            ",
            "conditionsGeneralNotes" => '
            <p>
            <ul>
            <li>Modifications (extra or less prices) during the engineering will be charged at your account. We need a supplement order before we start with the production.</li>
            <li>Unless otherwise agreed, penalties or costs for late delivery are being rejected by Plasticon.</li>
            <li>Our prices are including max. 2 revisions of the drawing(s). Extra revisions, based upon a modified scope, will lead to a surcharge.</li>
            <li>Concerning the guarantee, we would bring to your attention that our guarantee only counts for defects, directly caused by material, construction and/or manufacture mistakes. Plasticon is responsible for his own delivered material or parts of material. Plasticon will replace or re-deliver his materials, also will Plasticon execute the earlier assembly duties. The costs of the dismantling of the apparatus will not be covered by our guarantee. Modifications, not carried out by us, will release Plasticon from their guarantee. The maximum amount for our guarantee obligation shall in any case not be higher than the sales price of the concerned delivery. </li>
            <li>Whatever the circumstances, Plasticon will not be liable for any indirect or consequential loss or damage, such as loss of use, loss of production, loss of profits, loss of contracts…, and whether arising under guarantee, contract , tort, at law or otherwise.</li>
            <li>Storage of products longer than 4 weeks will lead to a surcharge.</li>
            </ul>
            </p>
            ',

            'generalRemarks' => "
            <p>
            <ul>
            <li>Design requirements, codes and standard mentioned in your inquiry and not mentioned in our proposal are not taken into account by Plasticon.</li>
            <li>Our offer is based on the state of the art and standards at the time the offer is submitted. Costs for subsequent changes shall be borne by the customer.</li>
            <li>Our offer only includes the manufacturing of vessels, apparatus, piping and linings and, if necessary, the procurement of the materials required for their manufacturing (in particular lining materials and resins) from third parties in accordance with the customers specifications. We do not make any recommendations regarding the selection of materials to be procured and processed for the manufacturing of vessels, apparatuses, piping and linings. Nor do we make any statements on the suitability of the materials for the purpose intended by the customer. The selection and assessment of the suitability of the materials is not part of our offer and is expressly excluded from it. The selection and assessment of the suitability of the materials is the sole responsibility of the customer. If it is found that the materials used for the manufacturing of the vessels, apparatuses, piping and linings are not suitable for the purpose intended by the customer, any liability of Plasticon Poland SV arising from or in connection with the selection of materials is excluded, except in cases of intent and gross negligence.</li>
            <li>Tanks will be marked with Plasticon logo.</li>
            </ul>
            </p>
            
            ",


                'technicalItems' => [
                    (object) [
                        "name" => "Item 1",
                        "general" => [
                            [ 'id'  => 0, 'type' => 'select', 'text' => 'Expert approval', 'options' => [
                                ['id' => 0, 'text' => 'not considered'],
                                ['id' => 1, 'text' => 'UDT'],
                                ['id' => 3, 'text' => 'PED'],
                                ['id' => 4, 'text' => 'TUV'],
                                ['id' => 5, 'text' => 'KIVA'],
                                ['id' => 6, 'text' => 'vlarem'],
                            ],
                            'selected' => 0,
                            'visible' => true,
                            'value' => 'not considered',    
                            ],
                            [ 'id'  => 1, 'type' => 'select', 'text' => 'Strength calculations', 'options' => [
                                ['id' => 0, 'text' => 'without calculation'],
                                ['id' => 1, 'text' => 'UDT'],
                                ['id' => 3, 'text' => 'EN 13121-3'],
                                ['id' => 4, 'text' => 'RTP-1 (only for vertical tank)'],
                            ],
                            'selected' => 0,
                            'visible' => true,
                            'value' => 'without calculation',    
                            ],
                            [ 'id'  => 2, 'type' => 'text', 'text' => 'Tolerances: According EN 13121-3', 'visible' => true],
                            [ 'id'  => 3, 'type' => 'select', 'text' => 'Installation', 'options' => [
                                ['id' => 0, 'text' => 'in the building'],
                                ['id' => 1, 'text' => 'outside the building'],
                            ],
                            'selected' => 0,
                            'visible' => true,
                            'value' => 'in the building',    
                            ],
                            [ 'id'  => 4, 'type' => 'text', 'text' => 'Earthquake: not considered', 'visible' => true],
                            [ 'id'  => 5, 'type' => 'text', 'text' => 'Additional loads: not considered', 'visible' => true],
                            
                            
                            [ 'id'  => 6, 'type' => 'select', 'text' => 'Leakage test', 'options' => [
                                ['id' => 0, 'text' => 'not considered'],
                                ['id' => 1, 'text' => 'Test acc. EN13121-3 at ambient temperature'],
                            ],
                            'selected' => 0,
                            'visible' => true,
                            'value' => 'not considered',    
                            ],

                            [ 'id'  => 7, 'type' => 'select', 'text' => 'Railing/ladder/platform', 'options' => [
                                ['id' => 0, 'text' => 'not considered'],
                                ['id' => 1, 'text' => 'acc. PN-EN 14122'],
                                ['id' => 1, 'text' => 'acc. DIN 28017'],
                            ],
                            'selected' => 0,
                            'visible' => true,
                            'value' => 'not considered',    
                            ],

                            [ 'id'  => 4, 'type' => 'text', 'text' => 'Foundation: EN 13121-4 § 5.3.2', 'visible' => true],
                        ],

                        "executionDescription" => "The tank will be executed according to received specification. The design includes flat bottom and dished top.",
                        'equipment' => [

                            [ 'id'  => 0, 'type' => 'select', 'text' => 'Nameplate', 'options' => [
                                ['id' => 0, 'text' => 'According to Plasticon standard'],
                                ['id' => 1, 'text' => 'Not included'],
                            ],
                            'selected' => 0,
                            'visible' => true,
                            'value' => 'According to Plasticon standard',    
                            ],

                            [ 'id'  => 1, 'type' => 'select', 'text' => 'Gaskets', 'options' => [
                                ['id' => 0, 'text' => 'Not included'],
                                ['id' => 1, 'text' => 'EPDM'],
                                ['id' => 2, 'text' => 'EPDM (only for manholes and covered nozzles)'],
                                ['id' => 3, 'text' => 'Viton'],
                                ['id' => 4, 'text' => 'Viton (only for manholes and covered nozzles)'],
                                ['id' => 5, 'text' => 'PTFE'],
                                ['id' => 6, 'text' => 'PTFE (only for manholes and covered nozzles)'],
                               
                            ],
                            'selected' => 0,
                            'visible' => true,
                            'value' => 'Not included',    
                            ],

                            [ 'id'  => 2, 'type' => 'select', 'text' => 'Screws / Nuts', 'options' => [
                                ['id' => 0, 'text' => 'Not included'],
                                ['id' => 1, 'text' => 'A2/A4'],
                                ['id' => 2, 'text' => 'A2/A4 (only for manholes and covered nozzles)'],
                                ['id' => 3, 'text' => 'A4'],
                                ['id' => 4, 'text' => 'A4 (only for manholes and covered nozzles)'],
                                ['id' => 5, 'text' => 'Fe/ZN9'],
                                ['id' => 6, 'text' => 'FE/ZN9 (only for manholes and covered nozzles)'],
                               
                            ],
                            'selected' => 0,
                            'visible' => true,
                            'value' => 'Not included',    
                            ],

                            [ 'id'  => 3, 'type' => 'select', 'text' => 'Lifting lugs', 'options' => [
                                ['id' => 0, 'text' => 'Not included'],
                                ['id' => 1, 'text' => '1.4301'],
                            ],
                            'selected' => 0,
                            'visible' => true,
                            'value' => 'Not included',    
                            ],


                            [ 'id'  => 4, 'type' => 'select', 'text' => 'Anchor brackets', 'options' => [
                                ['id' => 0, 'text' => 'Not included'],
                                ['id' => 1, 'text' => 'GRP with stainless steel including composite anchor cartridges'],
                                ['id' => 2, 'text' => '1.4301 with stainless steel including composite anchor cartridges'],
                                ['id' => 3, 'text' => 'GRP without stainless steel and composite anchor cartridges'],
                                ['id' => 4, 'text' => '1.4301 without stainless steel and composite anchor cartridges'],
                            ],
                            'selected' => 0,
                            'visible' => true,
                            'value' => 'Not included',    
                            ],

                            [ 'id'  => 5, 'type' => 'text', 'text' => 'Packaging: all the nozzles will be protected', 'visible' => true],
                            [ 'id'  => 6, 'type' => 'text', 'text' => 'Foundation protection plate: 3mm PE-HD', 'visible' => true],
                            [ 'id'  => 7, 'type' => 'text', 'text' => 'Railing: S235JR HDG', 'visible' => true],
                            [ 'id'  => 8, 'type' => 'text', 'text' => 'Railing: S235JR painted in Plasticon standard painting system ', 'visible' => true],
                            [ 'id'  => 9, 'type' => 'text', 'text' => 'Ladder: S235JR HDG ', 'visible' => true],
                            [ 'id'  => 10, 'type' => 'text', 'text' => 'Ladder: S235JR painted in Plasticon standard painting system ', 'visible' => true],
                            [ 'id'  => 11, 'type' => 'text', 'text' => 'Railing and ladder: S235JR HDG ', 'visible' => true],
                            [ 'id'  => 12, 'type' => 'text', 'text' => 'Railing and ladder: S235JR painted in Plasticon standard painting system ', 'visible' => true],
                            [ 'id'  => 13, 'type' => 'text', 'text' => 'Heating: by electric heating wire ', 'visible' => true],
                            [ 'id'  => 14, 'type' => 'text', 'text' => 'Agitator: included without ATEX ', 'visible' => true],
                            [ 'id'  => 15, 'type' => 'text', 'text' => 'Leak detection system: included (SGB without ATEX) ', 'visible' => true],

                        ],
                        
                       
                        "img" => null,
                        "svg" => "<svg xmlns='http://www.w3.org/2000/svg' width='350' height='350' viewBox='0 0 350 350'>
                        <!-- Body -->
                        <ellipse cx='200' cy='220' rx='120' ry='100' fill='#000000' />
                        
                        <!-- Head -->
                        <circle cx='200' cy='150' r='80' fill='#808080' />
                        
                        <!-- Ears -->
                        <path d='M140 90 L120 40 L160 90 Z' fill='#808080' />
                        <path d='M260 90 L280 40 L240 90 Z' fill='#808080' />
                        <path d='M145 85 L135 45 L160 85 Z' fill='#FFC0CB' />
                        <path d='M255 85 L265 45 L240 85 Z' fill='#FFC0CB' />
                        
                        <!-- Eyes -->
                        <circle cx='160' cy='140' r='15' fill='#FFFFFF' />
                        <circle cx='240' cy='140' r='15' fill='#FFFFFF' />
                        <circle cx='165' cy='140' r='8' fill='#000000' />
                        <circle cx='245' cy='140' r='8' fill='#000000' />
                        <circle cx='163' cy='137' r='3' fill='#FFFFFF' />
                        <circle cx='243' cy='137' r='3' fill='#FFFFFF' />
                        
                        <!-- Nose -->
                        <path d='M195 160 L205 160 L200 170 Z' fill='#FFC0CB' />
                        
                        <!-- Mouth -->
                        <path d='M180 180 Q200 190 220 180' fill='none' stroke='#000000' stroke-width='2' />
                        
                        <!-- Whiskers -->
                        <line x1='140' y1='170' x2='80' y2='160' stroke='#000000' stroke-width='2' />
                        <line x1='140' y1='175' x2='80' y2='175' stroke='#000000' stroke-width='2' />
                        <line x1='140' y1='180' x2='80' y2='190' stroke='#000000' stroke-width='2' />
                        
                        <line x1='260' y1='170' x2='320' y2='160' stroke='#000000' stroke-width='2' />
                        <line x1='260' y1='175' x2='320' y2='175' stroke='#000000' stroke-width='2' />
                        <line x1='260' y1='180' x2='320' y2='190' stroke='#000000' stroke-width='2' />
                        
                        <!-- Paws -->
                        <ellipse cx='150' cy='310' rx='30' ry='20' fill='#808080' />
                        <ellipse cx='250' cy='310' rx='30' ry='20' fill='#808080' />
                        
                        <!-- Tail -->
                        <path d='M300 250 Q330 230 350 260' fill='none' stroke='#808080' stroke-width='40' stroke-linecap='round' />
                    </svg>",
                        'medium' => 'HCl', // design_description_medium
                        'density' => '1300', //design_density_medium
                        'densityUnit' => 'kg/m³',

                        'pressureOperatingValue' => '0.5', // design_work_pressure_max
                        'pressureOperatingUnit' => 'Barg', // barg
                        'pressureDesignValue' => '0.8', //design_over_pressure 
                        'pressureDesignUnit' => 'Barg', // barg

                        'vaccumOperatingValue' => '-',
                        'vaccumOperatingUnit' => 'Barg',
                        'vaccumDesignValue' => '-',
                        'vaccumDesignUnit' => 'Barg',

                        'temperatureOperatingValue' => '26,96',
                        'temperatureOperatingUnit' => 'Barg',
                        'temperatureDesignValue' => '26,96',
                        'temperatureDesignUnit' => 'Barg',

                        'totalVolumeOperatingValue' => '26,96', 
                        'totalVolumeOperatingUnit' => 'm³', // m³
                        'totalVolumeDesignValue' => '26,96', // design_total_volume
                        'totalVolumeDesignUnit' => 'm³', // m³

                        'usefulVolumeOperatingValue' => '26,96',
                        'usefulVolumeOperatingUnit' => 'm³', // m³
                        'usefulVolumeDesignValue' => '26,96', // design_useful_volume
                        'usefulVolumeDesignUnit' => 'm³', // m³

                        'diameterOperatingValue' => '26,96',
                        'diameterOperatingUnit' => 'mm',
                        'diameterDesignValue' => '26,96', // design_inside_dim
                        'diameterDesignUnit' => 'mm', // 'mm'

                        'cylindricalHeightOperatingValue' => '26,96',
                        'cylindricalHeightOperatingUnit' => 'mm',
                        'cylindricalHeightDesignValue' => '26,96', //design_cylinder_height
                        'cylindricalHeightDesignUnit' => 'mm', // 'mm'

                        'totalHeightOperatingValue' => '26,96',
                        'totalHeightOperatingUnit' => 'mm',
                        'totalHeightDesignValue' => '26,96', // design_total_height
                        'totalHeightDesignUnit' => 'mm', // 'mm'

                        'weightOperatingValue' => '26,96',
                        'weightOperatingUnit' => 'kg',
                        'weightDesignValue' => '26,96', // tank-weight
                        'weightDesignUnit' => 'kg', //  'kg'

                        'corrosionBarrierOperatingValue' => '2,5mm',
                        'corrosionBarrierOperatingUnit' => 'Derakane 411-350',
                        'corrosionBarrierDesignValue' => '2,5mm',
                        'corrosionBarrierDesignUnit' => 'Derakane 411-350',

                        'interlayerOperatingValue' => '2,5mm',
                        'interlayerOperatingUnit' => 'Derakane 411-350',
                        'interlayerDesignValue' => '2,5mm',
                        'interlayerDesignUnit' => 'Derakane 411-350',

                        'structuralResinOperatingValue' => '2,5mm',
                        'structuralResinOperatingUnit' => 'Derakane 411-350',
                        'structuralResinDesignValue' => '2,5mm',
                        'structuralResinDesignUnit' => 'Derakane 411-350',

                        'topcoatOperatingValue' => '2,5mm',
                        'topcoatOperatingUnit' => 'Derakane 411-350',
                        'topcoatDesignValue' => '2,5mm',
                        'topcoatDesignUnit' => 'Derakane 411-350',

                        'additionalOperatingValue' => '2,5mm',
                        'additionalOperatingUnit' => 'Derakane 411-350',
                        'additionalDesignValue' => '2,5mm',
                        'additionalDesignUnit' => 'Derakane 411-350',


                        'notesLine1' => 'Nozzle fabrication according to DIN 16966 Part 6, drilling according to DIN 1092-1.',
                        'notesLine2' => 'All nozzles are protected with flange protection caps to prevent damage.',

                        'miscTable' => [
                            (object)[
                                'dn' => '25',
                                'description' => 'Fixed flange',
                                'quantity' => '1',
                                'equipment' => 'not considered',
                            ],
                        ],

                        'exclusions' => 'Transport, installation, heating, insulation, internals, elements not mentioned above, other gaskets and fasteners, post-curing.'
                    ],

                ],

            ],
            "project" => [
                [
                    'name' => "General Info",
                    'type' => "general",
                    'items' => [],
                    'submenu' => [
                        [
                            "name" => "General Info",
                            'active' => true,
                            'pernament' => true,
                            'type' => "general_info",
                        ],
                        [
                            "name" => "Clinet",
                            'active' => true,
                            'pernament' => true,
                            'type' => "general_client",
                        ],
                        [
                            "name" => "Sales Person",
                            'active' => true,
                            'pernament' => true,
                            'type' => "genral_sales",
                        ],
                        [
                            "name" => "Cover Letter",
                            'active' => true,
                            'type' => "general_coverletter",
                        ],
                        [
                            "name" => "General Remarks",
                            'active' => true,
                            'type' => "general_remarks",
                        ],
                    ]
                ],
                [
                    'name' => "Commercial",
                    'type' => "commercial",
                    'items' => [],
                    'submenu' => [
                        [
                            "name" => "Pricing",
                            'active' => true,
                            'type' => "commercial_pricing",
                        ],
                        [
                            "name" => "General Terms",
                            'active' => true,
                            'type' => "commercial_terms",
                        ],
                        [
                            "name" => "Conditions",
                            'active' => true,
                            'type' => "commercial_conditions",
                        ],
                       
                    ]
                ],
               
                
                [
                    "name" => "Technical Specifications",
                    'active' => true,
                    'type' => "technical",
                    "items" => [],
                ],
                [
                    "name" => "Documentation",
                    'active' => true,
                    'type' => "documentation",
                    "items" => [],
                ],
               
                
               
            ],

            "sections" => [
                [
                    'id' => 1,
                    'active' => false,
                    'title' => 'Cover Letter',
                ],
                [
                    'id' => 2,
                    'active' => true,
                    'title' => 'Table of contents',
                ],
                [
                    'id' => 3,
                    'active' => true,
                    'title' => 'Pricing',
                ],
                [
                    'id' => 4,
                    'active' => true,
                    'title' => 'Technical Specifications',
                ],
                [
                    'id' => 5,
                    'active' => true,
                    'title' => 'Documentation',
                ],
                [
                    'id' => 6,
                    'active' => true,
                    'title' => 'Timeline',
                ],
                [
                    'id' => 7,
                    'active' => true,
                    'title' => 'Commercial conditions',
                ],
                [
                    'id' => 8,
                    'active' => true,
                    'title' => 'General remarks',
                ],
            ]

        ];
    }

    public static function getLangSeed($language = "eng"){
        // ta funckja wzwraca zawsze ten sam seed, w przypadku uzupełnienia seedów o różne języki należy zaktualizować funckję
       
        $ret = self::getFieldSeed();
       
        switch ($language) {
            case 'eng':
                $ret = self::getFieldSeed();
                break;
            case 'pol':
                $ret = self::getFieldSeed();
                break;
            case 'ger':
                $ret = self::getFieldSeed();
                break;
            case 'nld':
                $ret = self::getFieldSeed();
                break;
            default:
                $ret = self::getFieldSeed();
                break;
        }

        return $ret;
    }

    public static function formatNumber($num, $decimalPlaces = 2)
    {
        $rounded = number_format((float)$num, $decimalPlaces, '.', '');
        list($integerPart, $decimalPart) = explode('.', $rounded);
        $withSpaces = preg_replace('/\B(?=(\d{3})+(?!\d))/', ' ', $integerPart);
        return $withSpaces . '.' . $decimalPart;
    }

    public static function getCrmData($crmId = null)
    {

        if ($crmId === null || !is_numeric($crmId)) {
            return [
                'success' => false,
                'msg' => "No CRM Id passed or CRM Id invalid",
                'data' => null
            ];
        }


        $crmOfferTable = cfg::getData("sql_tab", "offers_offers");
        $crmClientTable = cfg::getData("sql_tab", "offers_clients");

        $crmOfferDataSql = "SELECT * FROM $crmOfferTable WHERE id = '$crmId'";
        $crmOfferData = self::executeDatabaseQuery($crmOfferDataSql);
        if (!$crmOfferData['success']) {
            return [
                'success' => false,
                'msg' => "CRM Fetch error: " . $crmOfferData['error'],
                'data' => null
            ];
        }
        $crmClientData = null;
        $crmFinalClientData = null;

        if ($crmOfferData['row_count'] !== 0) {
            $crmClientDataSql = "SELECT * FROM $crmClientTable where id = " . $crmOfferData['data']['client'];
            $crmClientData = self::executeDatabaseQuery($crmClientDataSql);
            if (!$crmClientData['success']) {
                $crmClientData = null;
            }

            $crmFinalClientDataSql = "SELECT * FROM $crmClientTable where id = " . $crmOfferData['data']['finalClient'];
            $crmFinalClientData = self::executeDatabaseQuery($crmFinalClientDataSql);
            if (!$crmFinalClientData['success']) {
                $crmFinalClientData = null;
            }
        }




        return [
            'success' => true,
            'msg' => 'CRM Data successfully acquired',
            'data' => [
                'crm' => $crmOfferData,
                'client' => $crmClientData,
                'finalClient' => $crmFinalClientData
            ]
        ];
    }

    public static function getRevisionList($crmId = null)
    {
        if ($crmId === null || !is_numeric($crmId)) {
            return [
                'success' => false,
                'msg' => "No CRM Id passed or CRM Id invalid",
                'data' => null
            ];
        }

        $revisonsSql = "SELECT * FROM offerpdf.offers WHERE crm_id = '$crmId'";
        $revisions = self::executeDatabaseQuery($revisonsSql);
        if ($revisions['success'] === false) {
            return [
                'success' => false,
                'msg' => "No Revisions under CRM Id $crmId",
                'data' => null
            ];
        }


        return [
            'success' => true,
            'msg' => 'Revisions successfully acquired',
            'data' => [
                'revisions' => $revisions,

            ]
        ];
    }

    public static function getRevisionById($id = null)
    {
        if ($id === null || !is_numeric($id)) {
            return [
                'success' => false,
                'msg' => "No Revision Id passed or Revision Id invalid",
                'data' => null
            ];
        }

        $revisonSql = "SELECT * FROM offerpdf.offers WHERE id = $id";
        $revision = self::executeDatabaseQuery($revisonSql);
        if ($revision['success'] === false) {
            return [
                'success' => false,
                'msg' => "No Revision under Id $id",
                'data' => null
            ];
        }

        return [
            'success' => true,
            'msg' => 'Revision successfully acquired',
            'data' => [
                'revisions' => $revision,
            ]
        ];
    }

    public static function getGceData($id = null)
    {
       
        if ($id === null || !is_numeric($id)) {
            return [
                'success' => false,
                'msg' => "No CRM Id passed or CRM Id invalid",
                'data' => null
            ];
        }

        $gceCalculationSql = "SELECT id from gce.gce_offers where crm_id = $id";
        $gceCalculation = self::executeDatabaseQuery($gceCalculationSql);
        if ($gceCalculation['success'] === false || $gceCalculation['row_count'] === 0) {
            
            echo json_encode( [
                'success' => false,
                'msg' => "No GCE calculation under CRM Id $id",
                'data' => null
            ]);
            
            return [
                'success' => false,
                'msg' => "No GCE calculation under CRM Id $id",
                'data' => null
            ];
        }

        $primaryRevisionSql = "SELECT * from gce.gce_revisions where offer_id = " . $gceCalculation['data']['id'] . " and status = 1";
        $primaryRevision = self::executeDatabaseQuery($primaryRevisionSql);
        if ($primaryRevision['success'] === false) {
            
            
            return [
                'success' => false,
                'msg' => "No Primary Revision under GCE calculation Id " . $gceCalculation['data']['id'],
                'data' => null
            ];
        }
        $stateSql = "SELECT * from gce.gce_states where revision_id = " . $primaryRevision['data']['id'];
        $state = self::executeDatabaseQuery($stateSql);
        if ($state['success'] === false) {
            return [
                'success' => false,
                'msg' => "No State under Primary Revision Id " . $primaryRevision['data']['id'],
                'data' => null
            ];
        }
        $pricing = self::parseGceData($state['data']['payload']);

        echo json_encode([
            'success' => true,
            'msg' => "Gce Pricing data acquired",
            'data' => $pricing
        ]);

        return $pricing;
    }


    public static function parseGceData($payload)
    {
        /*
        $this->pricingTable = [
            ['number' => 1, 'name' => 'Tank', 'qty' => 1, 'ppa' => '10 000,55', 'total' => '10 000,55', 'currency' => '€'],
            ['number' => 2, 'name' => 'Transport', 'qty' => 1, 'ppa' => '5 300,45', 'total' => '5 300,45', 'currency' => '€'],
        ];
        $this->pricingTotal = '15 301,00';
        $this->pricingTotalQty = 1;
        $this->pricingTotalCurrency = '€';

        */

        $pricing = [
            'table' => [],
            'total' => 0,
            'totalQty' => 1,
            'totalCurrency' => '€'
        ];

        $payload = json_decode($payload);


        foreach ($payload->components as $index => $component) {
            $total = $component->temps->___final->price * 1;
            $ppa = $total / $component->isa * 1;
            $pricing['table'][] = ['number' => $index + 1, 'name' => $component->name, 'qty' => $component->isa, 'ppa' => $ppa, 'total' => $total, 'currency' => '€'];
            $pricing['total'] += $total;
        }


        return $pricing;
    }



    public static function domain()
    {
        $urls = (object)[
            "local" => "http://localhost/gce_dev/offer",
            "dev" => "http://************:8093/offer",
            "prod" => "https://gce.plasticon.app/offer"
        ];

        $environment = cfg::currentEnvironment();
        return $urls->{$environment};
    }
    public static function clearDomain()
    {
        $urls = (object)[
            "local" => "http://localhost/gce_dev/",
            "dev" => "http://************:8093/",
            "prod" => "https://gce.plasticon.app/"
        ];

        $environment = cfg::currentEnvironment();
        return $urls->{$environment};
    }

    public static function gceDomain()
    {
        $urls = (object)[
            "local" => "http://localhost/gce_dev/gce",
            "dev" => "http://************:8093/gce",
            "prod" => "https://gce.plasticon.app/gce"
        ];

        $environment = cfg::currentEnvironment();


        return $urls->{$environment};
    }


    public static function createNewRevision($data)
    {
        $offerTable = "offerpdf.offers";

        $isPrimary = 0;
        $primaryCheckSql = "SELECT id FROM $offerTable WHERE crm_id = $data->crmId AND is_primary = 1;";
        $primaryCheck = core::sql()->query($primaryCheckSql);
        if ($primaryCheck === false) {
            $isPrimary = 1;
        }

        $seed = self::getLangSeed($data->language);

        $placeholderPayload = json_encode([
            'metadata' => [
                'crmId' => $data->crmId,
                'crmNo' => $data->crmNo,
                'revision' => [
                    'name' => $data->name,
                    'createdAt' => date("Y-m-d H:i:s"),
                    'createdBy' => user::get()->author(),
                ],
                'language' => $data->language
            ],
            'fields' => $seed['fields'],
            'project' => $seed['project'],
            'sections' => $seed['sections'],
        ]);

        $inserOfferData = [
            "id" => NULL,
            "crm_id" => $data->crmId,
            "crm_number" => $data->crmNo,
            "status" => 1,
            "is_locked" => $data->locked,
            "is_final" => 0,
            "is_primary" => $isPrimary,
            "offer_name" => $data->name,
            "offer_description" => $data->description,
            "offer_comments" => "",
            "offer_payload" => $placeholderPayload,
            "users" => "",
            "author_id" => user::get()->id(),
            "author_name" => user::get()->name(),
            "author_date" => date("Y-m-d H:i:s"),
            "editor_id" => user::get()->id(),
            "editor_name" => user::get()->author(),
            "editor_date" => date("Y-m-d H:i:s"),

        ];

        $execInsertOfferData = core::sql()->insertTab("offerpdf.offers", $inserOfferData);
        if ($execInsertOfferData === false) {
            return json_encode([
                "success" => false,
                "message" => "Error creating new revision",
            ]);
        }


        return json_encode([
            "success" => true,
            "message" => "Revision created successfully",
            "revisionId" => $execInsertOfferData,
        ]);
    }



    public static function gceGetCurrentExchangeRate()
    {
        $rate = (object)[
            "rate" => 4.75,
            "status" => "default",
            "date" => "2024-01-01 00:00:00",
            "effectiveDate" => "2024-01-01",
            "number" => "",
            "editor_id" => 0,
            "editor_name" => "GCE system default",

        ];

        $url = 'http://api.nbp.pl/api/exchangerates/rates/A/EUR';
        $response = file_get_contents($url);
        if ($response === FALSE) {
            return $rate;
        }
        $data = json_decode($response, TRUE);
        if ($data === NULL) {
            return $rate;
        }
        $rate->rate = $data['rates'][0]['mid'];
        $rate->status = "current";
        $rate->date = date("Y-m-d");
        $rate->effectiveDate = $data['rates'][0]['effectiveDate'];
        $rate->number = $data['rates'][0]['no'];
        $rate->editor_id = user::get()->id();
        $rate->editor_name = user::get()->author();

        return json_encode($rate);
    }

    public static function devUrl()
    {
        return "http://localhost:3000/static/js/bundle.js";
    }

    public static function gceGetFiles($versionId)
    {

        $files = (object)[];
        // if (false) {
        if (cfg::currentEnvironment() === "local") {
            $files->js = gcehelper::devUrl();
        } else {
            $version = gcehelper::gceVersion($versionId);
            $files->css = $version['path']  . "/" . $version['css_name'];
            $files->js = $version['path']  . "/" . $version['js_name'];
        }
        return $files;
    }


    public static  function db($table)
    {
        $dbConfigPrefix = "gce_";

        $dbTables = [
            // CONFIG DD
            "config" => $dbConfigPrefix . "config",
            "sources" => $dbConfigPrefix . "sources",
            "segments" => $dbConfigPrefix . "segments",
            "articles" => $dbConfigPrefix . "articles",
            "competition" => $dbConfigPrefix . "competition",
            "units" => $dbConfigPrefix . "units",

            //GCE DB
            "offers" => $dbConfigPrefix . "offers",
            "revisions" => $dbConfigPrefix . "revisions",
            "states" => $dbConfigPrefix . "states",
            "versions" => $dbConfigPrefix . "versions",


            //RISK

            "assessments" => "risk.risk_assesments",

            //CASHFLOW
            "cashflow" => "cashflow.cashflow_flows",



        ];

        return $dbTables[$table];
    }







    public static function removeBackslashes(&$data)
    {
        if (is_array($data) || is_object($data)) {
            foreach ($data as &$value) {
                self::removeBackslashes($value);
            }
        } elseif (is_string($data)) {
            $data = stripslashes($data);
        }
    }
}

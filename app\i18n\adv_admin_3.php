<?php if(!defined('DS')){ exit(); }

$txt['rot_stat']="Statistik";
$txt['rot_stat_ogolne']="Allgemein:";
$txt['rot_stat_name']="Name:";
$txt['rot_stat_ilosc']="Anzahl:";
$txt['rot_stat_ilew']="Anzahl aller Elemente";
$txt['rot_stat_sumaw']="Summe der Abrufe";
$txt['rot_stat_sumak']="Summe der Klicks";
$txt['rot_stat_types']="Dateitypen:";
$txt['rot_stat_link']="Aussenverknuepfuengen";
$txt['rot_stat_nw']="Am haeufigsten angezeigt:";
$txt['rot_stat_nk']="Am haeufigsten angeklickt:";
$txt['rot_stat_kw']="Klicks zum Anzeigen:";
$txt['rot_types']="Typen der Banners:";
$txt['rot_types_name']="Name der Kategorie";
$txt['rot_types_ilosc']="Anzahl";
$txt['rot_types_typesp']="Dateitypen:";
$txt['rot_types_sod']="Breite ab:";
$txt['rot_types_px']="px";
$txt['rot_types_do']="bis:";
$txt['rot_types_wod']="Hoehe ab:";
$txt['rot_types_do']="bis:";
$txt['rot_types_max']="max.:";
$txt['rot_types_kb']="Kb)";
$txt['rot_types_wszystkie']="alle Banners";

$txt['rot_arch_lista']="Liste der Banners, Werbungen (";
$txt['rot_arch_typ']="Typen der Banners:";
$txt['rot_arch_id']="ID";
$txt['rot_arch_baner']="Banner";
$txt['rot_arch_aktywnosc']="Datum von bis";
$txt['rot_arch_status']="Status";
$txt['rot_arch_jezyk']="Sprache";
$txt['rot_arch_prio']="Priorität";
$txt['rot_arch_udzial']="Anteil";
$txt['rot_arch_wys']="Abrufe";
$txt['rot_arch_klik']="Klicks";
$txt['rot_arch_od']="von:";
$txt['rot_arch_do']="bis:";
$txt['rot_arch_astat']="Statistik";
$txt['rot_arch_listakat']="Liste der Kategorien der Wechsler/Informationen";
$txt['rot_arch_szuk_status1']="nur aktive";
$txt['rot_arch_szuk_status2']="nur inaktive";
$txt['rot_arch_szuk_status0']="--Status des Banners--";
$txt['rot_arch_szuk_dostepne']="nur verfügbar";
$txt['rot_arch_szuk_niedostepne']="nur nicht verfügbar";
$txt['rot_arch_szuk_dostepnosc']="--Zugang zum Abruf--";
$txt['rot_arch_lang']="--Sprachversion--";

$txt['rot_form_edycja']="Bearbeitung des Banners:";
$txt['rot_form_uzupelnij']="Daten ergaenzen";
$txt['rot_form_dodawanie']="Banner hinzufuegen:";
$txt['rot_form_dataod']="Zeige ab (Datum)";
$txt['rot_form_datado']="Gültigkeitsdatum, Zeige bis (Datum)";
$txt['rot_form_typ']="Typ des Baners/der Werbung*:";
$txt['rot_form_typwyb']="--auswaehlen--";
$txt['rot_form_tytul']="Titel/Name*:";
$txt['rot_form_linkg']="Verknuepfung auf dem Banner:";
$txt['rot_form_okno']="Fenster,in dem sich die Verknüpfung oeffnet";
$txt['rot_form_noweokno']="neues Fenster";
$txt['rot_form_tosamookno']="dasselbe Fenster";
$txt['rot_form_dotychczasowa']="bisherige Grafik:";
$txt['rot_form_grafika']="Grafik:*";
$txt['rot_form_wymiary']="Groesse:";
$txt['rot_form_mozliwosc']="(manuelle Aenderung nur fuer SWF moeglich)";
$txt['rot_form_wys']="Hoehe (px)";
$txt['rot_form_szer']="Breite (px)";
$txt['rot_form_tlo']="Hintergrund";
$txt['rot_form_tylko swf']="(nur fuer SWF)";
$txt['rot_form_przezroczysty']="transparent";
$txt['rot_form_link']="Kode zum Link/zur Grafik:";
$txt['rot_form_opis']="Beschreibung, Informationen:";
$txt['rot_form_czywys']="sollen Abrufe aufgezaehlt werden";
$txt['rot_form_wysw']="Anzahl der Abrufe";
$txt['rot_form_wyslimit']="Begrenzung der Abrufe (0 = keine Begrenzung)";
$txt['rot_form_czyklik']="sollen Klicks aufgezaehlt werden";
$txt['rot_form_klik']="Anzahl der Klicks";
$txt['rot_form_kliklimit']="Begrenzung der Klicks (0 = keine Begrenzung)";
$txt['rot_form_brak']="--keine--";
$txt['rot_form_najwyzszy']=" -  niedrigst";
$txt['rot_form_najnizszy']=" -  hoechst";
$txt['rot_form_priorytet']="Prioritaet";
$txt['rot_form_udzial']="Prozentanteil";
$txt['rot_form_dowolna']="--beliebig--";
$txt['rot_form_lang']="Sprachversion";
$txt['rot_form_lista']="Liste der Banners/Werbungen";
$txt['rot_form_reset']="Reset";

$txt['rot_form_artdo']="przypisany do artykułu/działu";
$txt['rot_form_pp']="baner w postaci pliku";
$txt['rot_form_pk']="lub w postaci kodu";

$txt['rot_form_zast']="tekst zastępczy/podpis:";
$txt['rot_form_section4']="dostępność:";
$txt['rot_form_section1']="konfiguracja pliku flash (swf):";
$txt['rot_form_section2']="liczniki i limity:";

$txt['rot_form_htyp']="Die zur Verfuegung stehenden Bannertypen hängen von der Konfiguration einer Website ab, die nach Wunsch erstellt wird";
$txt['rot_form_hadres']="Die Website-Adresse, an die das jeweilige Baner weiterleitet - Umleitung auf die Verknuepfung beim Anklicken des Banners. Betrifft keine SWF-Banners.";

$txt['rot_form_hgrafika']="Grafikdatei, die dem vorgegebenen Dateityp entspricht und die Groesse des Banners berücksichtigt";
$txt['rot_form_hwymiary']=" Das System erkennt die Groesse der Banners automatisch, skaliert sie nicht nur mit der Ausnahme der Dateien mt SWF-Format. Im Falle der SWF-Dateien kann die erkannte Groesse geandert werden (in von dem jeweiligen Bannertyp erlaubten Grenzen).";
$txt['rot_form_htlo']="Auswahl der Durchsichtigkeit bedeutet, dass die Hintergrundfarbe ignoriert wird";
$txt['rot_form_hlink']="Link, der den Aussenbanner anzeigt. Seine Angabe hat zur Folge, dass das eingebene Banner, eingegebene Veknuepfung auf dem Banner und andere Parameter des Banners ignoriert werden. Fuer diesen Typ von Banners besteht keine Moeglichkeit, die Klicks aufzuzaehlen";
$txt['rot_form_hopis']="Nur fuer administrative Anwendungen, z.B. Notieren von Informationen ueber die Quelle des Banners oder den Werbevertrag";
$txt['rot_form_hwys']="Angabe der Begrenzung der Abrufe bedeutet, dass Banner nur dann angezeigt werden kann, solange die Begrenzung hoeher als oder gleich ist wie die Anzahl der bisherigen Abrufe ist";
$txt['rot_form_hklik']="Angabe der Begrenzung der Klicks bedeutet, dass Banner nur dann angezeigt werden kann, solange die Begrenzung hoeher als oder gleich wie die Anzahl der bisherigen Klicks ist. Aufzaehlen der Klicks gilt nicht fuer die durch eine Verknuepfung eingefuehrte Banners sowie SWF Banners, die mit dieser Funktion nicht kompatibel sind";
$txt['rot_form_hpriorytet']="Diese Option wird beim Anzeigen mehrerer Banners auf einmal benutzt. Banners werden beginnend mit der hoechsten Prioritaet abgerufen. Banners mit der selben Prioritaet werden zufaellig ausgewaehlt";
$txt['rot_form_hlangt']="Es wird bezeichnet, ob ein Banner nur fuer eine Sprachversion angezeigt werden soll, oder fuer alle erhaeltichen";
$txt['rot_form_hudzial']="Diese Option wird  bei Anzeigen mehrerer Banners auf einmal benutzt. Banners werden beginnend mit der hoechsten Prioritaet genommen. Banners mit der selben Prioritaet werden zufaellig ausgewaehlt";

$txt['rot_zmiena_log']="Wechsler - Aenderung des Parameters:";
$txt['rot_usuna_log']="Wechsler - entfernen";
$txt['rot_img_brak']="Inkorrekter Dateityp - abgelehnt.";

?>
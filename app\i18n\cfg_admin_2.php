<?php if(!defined('DS')){ exit(); }

$txt['konfig_form_edycja']="Configuration edit";
$txt['konfig_form_tytul']="name of the www service";
$txt['konfig_form_htytul']="Default name, appears in the headline bar of www browsers and depending on the project, in the chosen parts of the www service";
$txt['konfig_form_tytulp']="prefix to the title";
$txt['konfig_form_htytulp']="After defining, the prefix is automatically added to the page title where the main title is replaced by other text. For example, a prefix can be a short name of the main service and the title of the sub-pages will be added to it on the sub-pages";
$txt['konfig_form_description']="description for the search engines";
$txt['konfig_form_hdescription']="Short (2-3 sentences) description of the sub-page for search engines";
$txt['konfig_form_keywords']="keywords for search engines";
$txt['konfig_form_hkeywords']="Group of chosen words - entries, phrases devided by commas characterising the www service. Not more than 20-30 entries";
$txt['konfig_form_contacto']="receiver email";
$txt['konfig_form_hkonakto']="Contact e-mail used as a recipient contact forms or notifications to the administrator";
$txt['konfig_form_contacte']="contact email";
$txt['konfig_form_hkonakte']="Contact email is used depending on the project , e.g. as a sender of the contact form or displayed in the www text";
$txt['konfig_form_contacta']="sender (signature)";
$txt['konfig_form_hkonakta']="Name of the sender, signature of the contact email";
$txt['konfig_form_lang']="default language version";
$txt['konfig_form_hlang']="Language version, which is automatically active when opening www service";
$txt['konfig_form_panel']="Administrative panel";
$txt['konfig_zap_zmiana_log']="configuration - modification of data";
$txt['konfig_form_kodstat']="website stats code";
$txt['konfig_form_hkodstat']="Code of websiteode statistics like Google Analytics. Can you put the code is automatically added to the end of each sub-pages to collect information about users using the Web";

$txt['konfig_form_smtp']="email authorization (SMTP data)";
$txt['konfig_form_hsmtp']="Required by some email servers";
$txt['konfig_form_smtphost']="Host:";
$txt['konfig_form_smtplogin']="Login:";
$txt['konfig_form_smtphaslo']="Password:";

$txt['konfig_form_tytulseo']="default title for search engines (SEO)";
$txt['konfig_form_tytulseoh']="Default text shown in the bar of a web browser - used for positioning the web";
$txt['konfig_form_smtpport']="port";
$txt['konfig_form_smtpszyfrowanie']="encryption";

$txt['konfig_form_meta']="additional code / meta tags to the HEAD section";
$txt['konfig_form_metah']="HTML code added to the HEAD section - such as markers of analytical systems or search engines";
$txt['konfig_form_robots']="set indexed by the search engines";
$txt['konfig_form_robotsh']="The ROBOTS - information for search engines about the content index";
$txt['konfig_form_timezone']="default zone";
$txt['konfig_form_timezoneh']="setting affects the display of the dates for the logged person or persons who do not have a defined time zone.";
$txt['konfig_form_maitenance']="service not available, the administrative work";
$txt['konfig_form_maitenanceh']="Restrict access to ordinary users of administrative work in time or for any other reason which can be given in the user information";
$txt['konfig_form_maitenanceinfo']="administrative work - information for users";

$txt['konfig_form_robots1']="index,follow (default)";
$txt['konfig_form_robots2']="index, nofollow";
$txt['konfig_form_robots3']="noindex, follow";
$txt['konfig_form_robots4']="noindex, nofollow";

$txt['konfig_form_db']="Database administration";
$txt['konfig_form_db_v']="Database version:";
$txt['konfig_form_db_t']="Number of tables:";
$txt['konfig_form_db_opt']="Optimize data";
$txt['konfig_form_db_opth']="Command under certain conditions can improve database tables effectively increasing its efficiency.";
$txt['konfig_form_db_opth2']="This command is useful for large databases, which are frequently updated and / or their components removed.";
$txt['konfig_form_db_np']="Repair tables";
$txt['konfig_form_db_nph']="Command is used to repair the corrupted database.";
$txt['konfig_form_db_nph2']="This command is useful if a database error messages ('... is marked as crashed and repaired Should Be'), which in some cases occur on some servers.";

$txt['konfig_db_optcom']="The database has been optimized";
$txt['konfig_db_optlog']="Optimize database";
$txt['konfig_db_repaircom']="The database has been repaired";
$txt['konfig_db_repairlog']="Optimize database";

?>
<?php if(!defined('DS')){ exit(); }

$txt['guestbook']="Guestbook:";
$txt['guestbook_dodaj']="Add your entry:";

$txt['guestbook_usuwanie_log']="Guestbook - removal of data";
$txt['guestbook_edycja_log']="guestbook - edition of data, id:";
$txt['guestbook_edycja_blad']="Incomplete or incorrect data!";

$txt['guestbook_form_autor']="entry author:";
$txt['guestbook_form_email']="email:";
$txt['guestbook_form_autor2']="author*";
$txt['guestbook_form_email2']="email";
$txt['guestbook_form_autor']="author of the entry:";
$txt['guestbook_form_email']="email:";
$txt['guestbook_form_www']="www page:";
$txt['guestbook_form_tresc']="content*:";
$txt['guestbook_form_pogrubiony']="put in bold text";
$txt['guestbook_form_pochyly']="put in text in italics";
$txt['guestbook_form_elwidoczny']="element visible for users";
$txt['guestbook_form_zapisz']="save";

$txt['guestbook_edycja']="Edit entry:";
$txt['guestbook_edycja_aby']="You must log in first to make an entry";
$txt['guestbook_edycja_powrot']="return to guestbook";

$txt['guestbook_arch_aby']="You must log in first to make an entry";
$txt['guestbook_arch_wpis']="entry:";
$txt['guestbook_arch_autor']="author:";
$txt['guestbook_arch_niezatw']="(not approved)";
$txt['guestbook_arch_zatw']="(approved)";
$txt['guestbook_arch_edytuj']="edit";
$txt['guestbook_arch_ip']="ip:";
$txt['guestbook_arch_host']="host:";
$txt['guestbook_arch_usun']="remove selected";
$txt['guestbook_arch_aby']="You must log in first to make an entry";

$txt['guestbook_zapisane']="Data has been saved";
$txt['guestbook_istnieje']="The same entry from you already exists!";
$txt['guestbook_niepelne']="Incomplete or incorrect data!";

?>
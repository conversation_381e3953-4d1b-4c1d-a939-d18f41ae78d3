<?php

function consoleLog($data)
{
    $file = "log.json";
    $prevContents = file_get_contents($file);
    $text = "";

    file_put_contents($file, ' ');

    if (is_array($data) || is_object($data)) {
        $text = json_encode($data, JSON_PRETTY_PRINT);
    } else {
        $text = $data;
    }

    if (!empty($text)) {
        file_put_contents($file, $prevContents . PHP_EOL . $text);
    }
}

<?php if(!defined('DS')){ exit(); }

$txt['forum_tyt']="DISCUSSION FORUM";
$txt['forum_types1']="Entries done by everyone";
$txt['forum_types2']="Entries only by logged-in members";
$txt['forum_types3']="Discussion finished";
$txt['forum_types4']="Removed";
$txt['forum_types5']="Only for administrators";

$txt['forum_kategorie']="Categories";
$txt['forum_tematy']="Topics";
$txt['forum_nowyt']="New topic";
$txt['forum_stat']="Statistics";
$txt['forum_login']="Log in";
$txt['forum_katadmin']="Administrative categories";
$txt['forum_dol']="down";
$txt['forum_gora']="up";

$txt['forum_stat_tyt']="Forum statistics";
$txt['forum_stat_ogol']="General data:";
$txt['forum_stat_iled']="Number of sections in the forum:";
$txt['forum_stat_ilet']="Number of topics in the forum:";
$txt['forum_stat_ilep']="Number of posts in the forum:";
$txt['forum_stat_ilejpg']="Number of jpg files attached to the statements:";
$txt['forum_stat_wys']="Sum total of the times the topics have been shown:";
$txt['forum_stat_iledyskutantow']="Number of debaters:";
$txt['forum_stat_najczesciej']="Debaters most frequently expressing their opinion:";
$txt['forum_stat_najwiekszet']="Topics with the highest number of posts:";
$txt['forum_stat_host']="Most frequent host:";
$txt['forum_stat_ip']="Most frequent IP:";

$txt['forum_wyszuk']="Search engine:";
$txt['forum_wyszuk_zakrotka']="The searched phrase is too short";
$txt['forum_wyszuk_fraza']="phrase:";
$txt['forum_wyszuk_wsrod']="among entries:";
$txt['forum_wyszuk_wszystkich']="all";
$txt['forum_wyszuk_zost']="recent";
$txt['forum_wyszuk_zostdni']="days";
$txt['forum_wyszuk_search']="search";
$txt['forum_wyszuk_wpolu']="in the field:";
$txt['forum_wyszuk_autor']="author";
$txt['forum_wyszuk_tytul']="entry heading";
$txt['forum_wyszuk_tresc']="content";
$txt['forum_wyszuk_host']="host";
$txt['forum_wyszuk_ip']="IP";
$txt['forum_wyszuk_uwaga']="CAUTION:";
$txt['forum_wyszuk_redakcja']="The editorial office is not responsible for the content of the statements.";
$txt['forum_wyszuk_zastrzegamy']="We reserve the right to remove entries without giving reason";
$txt['forum_wyszuk_legend']="LEGEND:";
$txt['forum_wyszuk_wyszukiwanie']="Search for a phrase:";
$txt['forum_wyszuk_wfraza']="phrase:";
$txt['forum_wyszuk_nieprawidlowa']="Incorrect phrase";

$txt['forum_czy']="Are you sure you want to remove selected information?";
$txt['forum_najnowsze']="Latest discussions:";
$txt['forum_tytul']="Heading:";
$txt['forum_autor']="Author:";
$txt['forum_odp']="Answer";
$txt['forum_wejsc']="Enter:";
$txt['forum_ostatni']="Last entry:";
$txt['forum_brakt']="no topics";
$txt['forum_ausunt']="remove";
$txt['forum_aprzeniest']="move";
$txt['forum_aedytujt']="edit";
$txt['forum_awykonaj']="execute";
$txt['forum_statusna']="status at:";
$txt['forum_przeniesdo']="move to:";
$txt['forum_katt']="Categories of topics";
$txt['forum_ilosct']="number of topics";
$txt['forum_razemt']="topics together:";
$txt['forum_brakd']="no defined categories";

$txt['forum_p_czy']="Are you sure you want to remove selected information?";
$txt['forum_p_zaznaczone']="selected:";
$txt['forum_p_ausun']="remove";
$txt['forum_p_azmiens']="change status";
$txt['forum_p_aprzenies']="move";
$txt['forum_p_aedytuj']="edit";
$txt['forum_p_awykonaj']="execute";
$txt['forum_p_statusna']="status at:";
$txt['forum_p_przeniesdo']="move to:";

$txt['forum_d_edycja']="Edit forum section";
$txt['forum_d_dodawanie']="Add a section to the forum";
$txt['forum_d_kategoria']="category *";
$txt['forum_d_nrpoz']="reference number *";
$txt['forum_d_name']="name *";
$txt['forum_d_opis']="description:";
$txt['forum_d_zapisz']="save";
$txt['forum_d_pozycje']="items marked with * are required";
$txt['forum_d_czy']="Are you sure you want to remove this element?";
$txt['forum_d_usund']="remove this section";
$txt['forum_d_wybrany']="Selected section can be removed only when it contains no topics";
$txt['forum_d_dodaj']="add a new section";
$txt['forum_d_istniejace']="Existing forum sections:";
$txt['forum_d_brak']="no data";
$txt['forum_d_dodawanie_log']="forum - add a section:";
$txt['forum_d_edycja_log']="forum - edit section:";
$txt['forum_d_zostaly']="Data has been saved";
$txt['forum_d_istnieje']="The name already exists, data has not been saved!";
$txt['forum_d_braknazwy']="No name!";
$txt['forum_d_usuniety']="Section has been removed";
$txt['forum_d_usuniety_log']="forum - removing the section with the id:";
$txt['forum_d_niepusty']="Section is not empty, it could not be removed!";

$txt['forum_pform_edycjat']="Edit forum topics";
$txt['forum_pform_dodawaniet']="Add a forum topic";
$txt['forum_pform_edycjap']="Edit forum statements";
$txt['forum_pform_dodawaniep']="Add a forum statement";
$txt['forum_pform_wodp']="in reply to";
$txt['forum_pform_kto']="who:";
$txt['forum_pform_email']="email:";
$txt['forum_pform_t']="topic:";
$txt['forum_pform_przyklejony']="topic pegged";
$txt['forum_pform_t']="topic:";
$txt['forum_pform_kat']="category:";
$txt['forum_pform_status']="status:";
$txt['forum_pform_tresc']="content:";
$txt['forum_pform_wgruby']="Puts in bold text";
$txt['forum_pform_wpochyly']="Puts in text in italics";
$txt['forum_pform_pogrubiony']="put in bold text";
$txt['forum_pform_pochyly']="put in text in italics";
$txt['forum_pform_usunjpg']="remove/replace jpg file";
$txt['forum_pform_jpg']="JPG picture:";
$txt['forum_pform_minimalny']="minimum picture size";
$txt['forum_pform_pszer']="pixels width and";
$txt['forum_pform_pwys']="height.";
$txt['forum_pform_obrazek']="Picture above";
$txt['forum_pform_przeskalowany']="pixels will get automatically rescaled.";
$txt['forum_pform_obrazkow']="JPG pictures during a month)<br />Think before you paste a picture, don't litter the forum :)";
$txt['forum_pform_umiescjpg']="put a JPG:";
$txt['forum_pform_jesli']="Unless indicated differently, the picture will be put at the bottom of the post";
$txt['forum_pform_przekroczyles']="Limit exceeded!";
$txt['forum_pform_przekroczyles2']="JPG pictures during a month!!!";
$txt['forum_pform_zapisz']="save";
$txt['forum_pform_cytuj']="Cite";
$txt['forum_pform_pozycje']="*Items written in bold have to be filled in.";
$txt['forum_pform_prawidlowe']="-correct email addresses ( @ ) and www (starting with 'http://') seperated from the text with spaces will automatically be turned into links";
$txt['forum_pform_wodp']="in response to";

$txt['forum_dodajt_zapisany']="Topic has been saved";
$txt['forum_dodajt_blad']="Error - data has not been saved";
$txt['forum_dodajt_istnieje']="Error - given topic already exists";
$txt['forum_dodajt_niepelne']="Error - incomplete or incorrect data";
$txt['forum_dodajt_zapisany']="Topic has been saved";
$txt['forum_dodajt_istnieje']="Error - given topic already exists here";
$txt['forum_dodajt_niepelne']="Error - incomplete or incorrect data";

$txt['forum_dodajp_zapisany']="Post has been saved";
$txt['forum_dodajp_blad']="Error - data has not been saved";
$txt['forum_dodajp_istnieje']="Error - you already have a post with this text for this topic!";
$txt['forum_dodajp_niepelne']="Error - incomplete or incorrect data";
$txt['forum_dodajp_zapisany']="Post has been saved";
$txt['forum_dodajp_niepelne']="Error - incomplete or incorrect data";

$txt['forum_usunt_log']="forum - removing topic (id:";
$txt['forum_usunt']="Removing the topics has been completed";
$txt['forum_statust_log']="forum - modification of the topic status";
$txt['forum_statust']="Modofication of the status has been executed";
$txt['forum_dzialt_log']="forum - change of the topic position";
$txt['forum_dzialt']="Change of the position has been executed";
$txt['forum_dzialt_blad']="Error - incorrect section";
$txt['forum_usunp_log']="forum - removal of posts";
$txt['forum_usunp']="Posts have been removed";
$txt['forum_typp_log']="forum - modification of the post status";
$txt['forum_typp']="Modification of the status has been executed";
$txt['forum_przeniesp_log']="forum - relocation of topics";
$txt['forum_przeniesp']="Relocation of topics has been executed";
$txt['forum_przeniesp_blad']="Error - incorrect topic";

$txt['forum_awytnij']="cut selected";
$txt['forum_awklej']="paste here";
$txt['forum_wyt']="Data ready to be moved";
$txt['forum_wyt_brak']="No data";

?>
[{"name": "myclabs/deep-copy", "version": "1.7.0", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/myclabs/DeepCopy.git", "reference": "3b8a3a99ba1f6a3952ac2747d989303cbd6b7a3e"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/myclabs/DeepCopy/zipball/3b8a3a99ba1f6a3952ac2747d989303cbd6b7a3e", "reference": "3b8a3a99ba1f6a3952ac2747d989303cbd6b7a3e", "shasum": ""}, "require": {"php": "^5.6 || ^7.0"}, "require-dev": {"doctrine/collections": "^1.0", "doctrine/common": "^2.6", "phpunit/phpunit": "^4.1"}, "time": "2017-10-19T19:58:43+00:00", "type": "library", "installation-source": "dist", "autoload": {"psr-4": {"DeepCopy\\": "src/DeepCopy/"}, "files": ["src/DeepCopy/deep_copy.php"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "description": "Create deep copies (clones) of your objects", "keywords": ["clone", "copy", "duplicate", "object", "object graph"]}, {"name": "paragonie/random_compat", "version": "v2.0.18", "version_normalized": "********", "source": {"type": "git", "url": "https://github.com/paragonie/random_compat.git", "reference": "0a58ef6e3146256cc3dc7cc393927bcc7d1b72db"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/paragonie/random_compat/zipball/0a58ef6e3146256cc3dc7cc393927bcc7d1b72db", "reference": "0a58ef6e3146256cc3dc7cc393927bcc7d1b72db", "shasum": ""}, "require": {"php": ">=5.2.0"}, "require-dev": {"phpunit/phpunit": "4.*|5.*"}, "suggest": {"ext-libsodium": "Provides a modern crypto API that can be used to generate random bytes."}, "time": "2019-01-03T20:59:08+00:00", "type": "library", "installation-source": "dist", "autoload": {"files": ["lib/random.php"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "Paragon Initiative Enterprises", "email": "<EMAIL>", "homepage": "https://paragonie.com"}], "description": "PHP 5.x polyfill for random_bytes() and random_int() from PHP 7", "keywords": ["csprng", "polyfill", "pseudorandom", "random"]}, {"name": "setasign/fpdi", "version": "v2.3.3", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/Setasign/FPDI.git", "reference": "50c388860a73191e010810ed57dbed795578e867"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/Setasign/FPDI/zipball/50c388860a73191e010810ed57dbed795578e867", "reference": "50c388860a73191e010810ed57dbed795578e867", "shasum": ""}, "require": {"ext-zlib": "*", "php": "^5.6 || ^7.0"}, "conflict": {"setasign/tfpdf": "<1.31"}, "require-dev": {"phpunit/phpunit": "~5.7", "setasign/fpdf": "~1.8", "setasign/tfpdf": "1.31", "tecnickcom/tcpdf": "~6.2"}, "suggest": {"setasign/fpdf": "FPDI will extend this class but as it is also possible to use TCPDF or tFPDF as an alternative. There's no fixed dependency configured."}, "time": "2020-04-28T12:40:35+00:00", "type": "library", "installation-source": "dist", "autoload": {"psr-4": {"setasign\\Fpdi\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://www.setasign.com"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://www.setasign.com"}], "description": "FPDI is a collection of PHP classes facilitating developers to read pages from existing PDF documents and use them as templates in FPDF. Because it is also possible to use FPDI with TCPDF, there are no fixed dependencies defined. Please see suggestions for packages which evaluates the dependencies automatically.", "homepage": "https://www.setasign.com/fpdi", "keywords": ["fpdf", "fpdi", "pdf"]}, {"name": "psr/log", "version": "1.1.3", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/php-fig/log.git", "reference": "0f73288fd15629204f9d42b7055f72dacbe811fc"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/php-fig/log/zipball/0f73288fd15629204f9d42b7055f72dacbe811fc", "reference": "0f73288fd15629204f9d42b7055f72dacbe811fc", "shasum": ""}, "require": {"php": ">=5.3.0"}, "time": "2020-03-23T09:12:05+00:00", "type": "library", "extra": {"branch-alias": {"dev-master": "1.1.x-dev"}}, "installation-source": "dist", "autoload": {"psr-4": {"Psr\\Log\\": "Psr/Log/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "PHP-FIG", "homepage": "http://www.php-fig.org/"}], "description": "Common interface for logging libraries", "homepage": "https://github.com/php-fig/log", "keywords": ["log", "psr", "psr-3"]}, {"name": "mpdf/mpdf", "version": "v8.0.6", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/mpdf/mpdf.git", "reference": "d27aa93513b915896fa7cb53901d3122e286f811"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/mpdf/mpdf/zipball/d27aa93513b915896fa7cb53901d3122e286f811", "reference": "d27aa93513b915896fa7cb53901d3122e286f811", "shasum": ""}, "require": {"ext-gd": "*", "ext-mbstring": "*", "myclabs/deep-copy": "^1.7", "paragonie/random_compat": "^1.4|^2.0|9.99.99", "php": "^5.6 || ~7.0.0 || ~7.1.0 || ~7.2.0 || ~7.3.0 || ~7.4.0", "psr/log": "^1.0", "setasign/fpdi": "^2.1"}, "require-dev": {"mockery/mockery": "^0.9.5", "mpdf/qrcode": "^1.0.0", "phpunit/phpunit": "^5.0", "squizlabs/php_codesniffer": "^3.5.0", "tracy/tracy": "^2.4"}, "suggest": {"ext-bcmath": "Needed for generation of some types of barcodes", "ext-xml": "Needed mainly for SVG manipulation", "ext-zlib": "Needed for compression of embedded resources, such as fonts"}, "time": "2020-05-25T09:08:39+00:00", "type": "library", "extra": {"branch-alias": {"dev-development": "7.x-dev"}}, "installation-source": "dist", "autoload": {"psr-4": {"Mpdf\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["GPL-2.0-only"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "role": "<PERSON><PERSON><PERSON>, maintainer"}, {"name": "<PERSON>", "role": "<PERSON><PERSON><PERSON> (retired)"}], "description": "PHP library generating PDF files from UTF-8 encoded HTML", "homepage": "https://mpdf.github.io", "keywords": ["pdf", "php", "utf-8"]}]
<?php
if (!defined('DS')) {
    exit();
}

core::loadClass('layout');

class layout_admin extends layout
{

    /**
     * display system comments
     * @param array $comm
     * @return html
     */
    public function displayComm($comm = array())
    {

        $html = "";

        foreach ($comm as $key => $val) {
            switch ($val['typ']) {
                case 'error':
                    $html .= "<div class=\"error\">" . $val['txt'] . "</div>";
                    break;

                default:
                    $html .= $val['txt'] . "<br />";
            }
        }

        return $html;
    }

    /**
     * show art structure as tree view
     */
    public function artTree()
    {

        cfg::loadFile(cfg::getData('mod_kat') . "art/conf.php");
        $id_d = request::getVar('id_d', 'id_d');
        $d_tab = cfg::getData('art_cfg', 'd_tab');

        if (!empty($d_tab) && !empty($id_d) && !empty($d_tab[$id_d])) {

            cfg::setJs("public/js/menu/dynamictree.js");

            $query = "SELECT a.* FROM " . cfg::getData('sql_tab', 'art') . " a WHERE a.lang='" . lang::getLang() . "' AND a.id_d='" . $id_d . "' AND (a.id_parent=0 OR (SELECT COUNT(p.id) FROM " . cfg::getData('sql_tab', 'art') . " p WHERE p.id=a.id_parent AND p.submenu_order>0)=0) ";
            if (cfg::getData('art_cfg', 'drzewo_levels')) {
                $query .= " AND a.level<='" . cfg::getData('art_cfg', 'drzewo_levels') . "'";
            }

            $zap = core::sql()->zap($query . " ORDER BY a.order_nr,a.id");

            while ($dane = core::sql()->fetchAssoc($zap)) {
                $levels[$dane['id_parent']][$dane['id']] = $dane;
            }

            core::sql()->freeResult($zap); ?>

            <div class="card">

                <div class="card-header bg-transparent header-elements-inline">
                    <span class="text-uppercase font-size-sm font-weight-semibold">Struktura treści</span>
                </div>

                <div class="card-body">

                    <div id="tree_kontener">
                        <div class="DynamicTree">
                            <div class="top b"><?php echo "<a href=\"" . request::actionUrl(array("act" => "artadmin", "id_d" => $id_d)) . "\">" . $d_tab[$id_d] . "</a>"; ?></div>
                            <div class="wrap" id="tree"><?php
                                                        if (!empty($levels) && is_array($levels)) {
                                                            $this->artTreeLevel($id_d, $levels, 0, 1);
                                                        }
                                                        ?></div>
                        </div>
                    </div>
                    <script>
                        var tree = new DynamicTree('tree');
                        tree.treename = 'art<?php echo $id_d . "_" . lang::getLang(); ?>';
                        tree.cookieDomain = conf.domain;
                        tree.cookiePath = "/";
                        tree.init();
                    </script>

                </div>

            </div><?php
                }
            }

            /**
             * tree view element
             * @param int $id_d
             * @param array $tab
             * @param int $key
             * @param int $level
             */
            public function artTreeLevel($id_d, $tab, $key, $level)
            {

                $id = (int) request::getVar('id_art', 'id_art');

                if (!empty($tab[$key]) && is_array($tab[$key])) {

                    foreach ($tab[$key] as $key2 => $val2) {
                        echo "<div class=\"folder\">&lt;a ";
                        if ($id == $key2) {
                            echo " class=\"b\"";
                        }
                        echo "href=\"" . request::actionUrl(array("act" => "artadmin", "id_art" => $key2, "id_d" => $id_d));
                        echo "\"&gt;" . strip_tags($val2['name']) . "&lt;/a&gt;\n";
                        $this->artTreeLevel($id_d, $tab, $key2, $level + 1);
                        echo "</div>";
                    }
                }
            }

            /**
             * show admin main menu element
             * @param string $icon
             * @param string $name
             * @param string $url
             * @param array $tab
             * @param string $add
             * @return html
             */
            public function adminMenuEl($id, $icon = "", $name = "", $url = "", $tab = "", $add = "")
            {
                $html = "";
                $preparedHtml = '';
                if (!empty($name)) {
                    if (empty($url)) {
                        $html .= "<li class=\"nav-item d-flex justify-content-between\"><div class=\"text-uppercase font-size-xs line-height-xs\">" . $name . "</div> <i class=\"icon-menu\" title=\"" . txt::doHtml($name) . "\"></i></li>";
                    } else {

                        $isSubMenu = !empty($tab) && is_array($tab) ? 'nav-item-submenu' : '';
                        $subItems = $isSubMenu ? $this->adminMenuEl2($tab, $id) : '';

                        $isActive  = cfg::getData('module_active') == $id || (!cfg::getData('module_active') && request::getCookie('mmenu') == $id) ? 'active' : '';

                        $isPopup = controls::checkState('stateconf', 'mainsidebarxs') || (core::getLayout() == "calendar") ? "data-popup='tooltip' data-placement='right' title='" . txt::doHtml($name) : '';

                        $href = "<a href='$url' class='nav-link d-flex align-items-center border-bottom'><i class='icon-$icon'></i><span>$name $add</span></a>";

                        if ($id == 'home') {
                            $preparedHtml = "<li class='nav-item' data-id='$id' $isPopup><div class='d-flex d-md-block justify-content-between align-items-center p-0'>$href<button type='button' class='btn btn-flat-white btn-icon btn-sm rounded-pill border-transparent sidebar-mobile-main-toggle d-lg-none'><i class='ph-x'></i></button></div></li>";
                        } else {
                            $preparedHtml = "<li class='nav-item $isSubMenu $isActive' data-id='$id' $isPopup>$href $subItems</li>";
                        }
                        // $html .= "</li>";
                    }
                }
                return $preparedHtml;
            }

            /**
             * admin submenu
             * @param array $tab
             * @return html
             */
            public function adminMenuEl2($tab, $id)
            {
                $html = "<ul class=\"nav-group-sub collapse\" ";

                if (cfg::getData('module_active') == $id || (!cfg::getData('module_active') && request::getCookie('menu') == $id)) {
                    // $html .= " style=\"display:block\"";
                } else {
                    // $html .= " style=\"display:none\"";
                }

                $html .= ">";

                foreach ($tab as $key => $val) {
                    if (empty($val['name'])) {
                        $html .= "<li class=\"nav-item-divider\"></li>";
                    } else if (!empty($val['link'])) {
                        $html .= "<li class=\"nav-item";

                        if (!empty($val['disabled'])) {
                            $html .= " disabled";
                        } else if ($val['link'] == cfg::getData('action_active')) {
                            $html .= " active";
                        }

                        $html .= "\"><a class=\"nav-link\" href=\"" . $val['link'] . "\"";

                        if (isset($val['target'])) {
                            $html .= " target=\"" . $val['target'] . "\"";
                        }

                        $html .= ">" . $val['name'] . " ";

                        if (!empty($val['add'])) {
                            $html .= $val['add'];
                        }

                        $html .= "</a>";

                        if (!empty($val['tab']) && is_array($val['tab'])) {
                            $html .= $this->adminMenuEl2($val['tab']);
                        }

                        $html .= "</li>";
                    }
                }

                $html .= "</ul>";

                return $html;
            }

            /**
             * admin main menu - show active modules with privileges
             */
            public function adminMenuA()
            {

                if (user::get()->logged()) {

                    echo $this->adminMenuEl("home", "home4", "Dashboard", request::actionUrl(array("act" => "users_panel")));

                    //if (user::get()->adminU()) {
                    echo "<li class=\"nav-item-header\"><div class=\"text-uppercase fs-sm lh-sm opacity-50 sidebar-resize-hide\">WORK AREA</div><i class=\"ph-dots-three sidebar-resize-show\" title=\"ph-dots-three sidebar-resize-show\" data-original-title=\"WORK AREA\"></i>";

                    if (user::get()->gceView()) {
                        echo '<li class="nav-item" data-id="uadmin-history"><a href="gce" class="nav-link d-flex align-items-center border-bottom"><i class="icon-file-spreadsheet"></i><span>Offers</span></a> </li>';
                        echo '<li class="nav-item" data-id="uadmin-history"><a href="gce/riskassesment" class="nav-link d-flex align-items-center border-bottom"><i class="icon-stats-bars"></i><span>Risk Assesment</span></a> </li>';
                    }

                    if (user::get()->configView()) {
                        echo '<li class="nav-item" data-id="uadmin-history"><a href="config" class="nav-link d-flex align-items-center border-bottom"><i class="icon-pencil-ruler"></i><span>Config</span></a> </li>';
                    }

                    //   }


                    if (user::get()->adminU()) {
                        echo "<li class=\"nav-item-header\"><div class=\"text-uppercase fs-sm lh-sm opacity-50 sidebar-resize-hide\">ADMIN AREA</div> <i class=\"ph-dots-three sidebar-resize-show\" title=\"\" data-original-title=\"ADMIN AREA\"></i></li>";
                    }

                    if (user::get()->adminU()) {

                        $submenu = array();

                        $submenu[] = array("link" => request::actionUrl(array("act" => "uadmin")), "name" => lang::txt('nagla_menu_u_arch'));
                        $submenu[] = array("link" => request::actionUrl(array("act" => "users_aadd")), "name" => lang::txt('nagla_menu_u_dodaj'));
                        $submenu[] = array("link" => request::actionUrl(array("act" => "uadmin_stats")), "name" => lang::txt('nagla_menu_u_stat'));

                        if (cfg::getData('u_cfg', 'banowanie')) {
                            $submenu[] = array("link" => request::actionUrl(array("act" => "uadmin_bansarch")), "name" => lang::txt('nagla_menu_u_filtr'));
                        }

                        if (core::isMod('groupsadmin') && user::get()->upr(3)) {

                            $submenu[] = array("link" => "", "name" => "");
                            $submenu[] = array("link" => request::actionUrl(array("act" => "groupsadmin_types")), "name" => "Kategorie grup");

                            $ile = core::sql()->count("id", " FROM " . cfg::getData('sql_tab', 'groups') . " WHERE status=2");

                            $add = "";

                            if ($ile) {
                                $add = "<span class=\"badge bg-info align-self-center ml-auto\">" . $ile . "</span>";
                            }

                            $submenu[] = array("link" => request::actionUrl(array("act" => "groupsadmin")), "name" => "Wszystkie grupy", "add" => $add);
                            $submenu[] = array("link" => request::actionUrl(array("act" => "groupsadmin_add")), "name" => "Dodaj grupę");
                        }

                        echo $this->adminMenuEl("users", "users", lang::txt('nagla_menu_u'), "#", $submenu);
                    }

                    if (core::isMod('art') && user::get()->upr(11)) {

                        $submenu = array();

                        cfg::loadFile(cfg::getData('mod_kat') . "art/conf.php");
                        $d_tab = cfg::getData('art_cfg', 'd_tab');

                        foreach ($d_tab as $key3 => $val3) {
                            $submenu[] = array("link" => request::actionUrl(array("act" => "artadmin", "id_d" => $key3)), "name" => $val3);
                        }

                        $submenu[] = array("link" => "", "name" => "");
                        $submenu[] = array("link" => request::actionUrl(array("act" => "artadmin_stats")), "name" => lang::txt('nagla_menu_cms_stat'));

                        echo $this->adminMenuEl("artadmin", "stack", "Artykuły i treść", "#", $submenu);
                    }


                    if (core::isMod('pool') && user::get()->upr(15)) {

                        $submenu = array();

                        cfg::loadFile(cfg::getData('mod_kat') . "pool/conf.php");
                        $tab = cfg::getData('pool_cfg', 'tab');

                        foreach ($tab as $key3 => $val3) {
                            $submenu[] = array("link" => request::actionUrl(array("act" => "pooladmin", "id_cat" => $key3)), "name" => $val3);
                        }

                        $submenu[] = array("link" => "", "name" => "");
                        $submenu[] = array("link" => request::actionUrl(array("act" => "pooladmin_add")), "name" => lang::txt('nagla_menu_a_dodaj'));

                        $submenu[] = array("link" => request::actionUrl(array("act" => "pooladmin_stats")), "name" => lang::txt('nagla_menu_a_stat'));

                        echo $this->adminMenuEl("pool", "list3", lang::txt('nagla_menu_a'), "#", $submenu);
                    }


                    if (core::isMod('newsletter') && (user::get()->upr(16) || user::get()->upr(10))) {

                        $submenu = array();

                        $submenu[] = array("link" => request::actionUrl(array("act" => "newsletteradmin_kat")), "name" => "Kategorie adresów e-mail");
                        $submenu[] = array("link" => request::actionUrl(array("act" => "newsletteradmin")), "name" => "Wszystkie adresy e-mail");

                        if (user::get()->upr(16)) {
                            $submenu[] = array("link" => request::actionUrl(array("act" => "newsletteradmin_warch")), "name" => "Lista wiadomości");
                            $submenu[] = array("link" => request::actionUrl(array("act" => "newsletteradmin_wadd")), "name" => lang::txt('nagla_menu_sub_w'));
                        }

                        echo $this->adminMenuEl("newsletter", "envelop", lang::txt('nagla_menu_sub'), "#", $submenu);
                    }

                    if (core::isMod('axp') && user::get()->upr(17)) {

                        $submenu = array();

                        $submenu[] = array("link" => request::actionUrl(array("act" => "axpadmin_types")), "name" => "Kategorie banerów");
                        $submenu[] = array("link" => request::actionUrl(array("act" => "axpadmin")), "name" => lang::txt('nagla_menu_axp_arch'));
                        $submenu[] = array("link" => request::actionUrl(array("act" => "axpadmin_add")), "name" => lang::txt('nagla_menu_axp_dodaj'));
                        $submenu[] = array("link" => "", "name" => "");
                        $submenu[] = array("link" => request::actionUrl(array("act" => "axpadmin_stats")), "name" => lang::txt('nagla_menu_axp_stat'));

                        echo $this->adminMenuEl("axp", "file-eye", lang::txt('nagla_menu_rot'), "#", $submenu);
                    }

                    if (core::isMod('shopadmin') && user::get()->upr(20)) {

                        $submenu = array();

                        $submenu[] = array("link" => request::actionUrl(array("act" => "shopadmin_types")), "name" => lang::txt('nagla_menu_shop_kat') . "Kategorie produktów");

                        if (core::isMod('productsadmin')) {
                            $submenu[] = array("link" => request::actionUrl(array("act" => "productsadmin")), "name" => lang::txt('nagla_menu_shop_products') . "Produkty");
                        }
                        if (core::isMod('producersadmin')) {
                            $submenu[] = array("link" => request::actionUrl(array("act" => "producersadmin")), "name" => lang::txt('nagla_menu_producers') . "Producenci");
                        }
                        if (core::isMod('shopcodesadmin')) {
                            $submenu[] = array("link" => request::actionUrl(array("act" => "shopcodesadmin")), "name" => lang::txt('nagla_menu_codes') . "Kody rabatowe");
                        }

                        $submenu[] = array("link" => "", "name" => "");

                        $submenu[] = array("link" => request::actionUrl(array("act" => "shopadmin_stats")), "name" => lang::txt('nagla_menu_shop_stat') . "Statystyka");
                        $submenu[] = array("link" => request::actionUrl(array("act" => "shopadmin_comparison")), "name" => lang::txt('nagla_menu_shop_comparison') . "Porównywarki cen");

                        echo $this->adminMenuEl("shop", "bag", lang::txt('nagla_menu_shop') . "Sklep", "#", $submenu);

                        if (core::isMod('ordersadmin') && user::get()->upr(21)) {

                            $submenu = array();

                            $add = "";

                            $ile = core::sql()->count("id", " FROM " . cfg::getData('sql_tab', 'shop_orders') . " WHERE status=1");
                            if ($ile) {
                                $add = "<span class=\"badge align-self-center ml-auto bg-info\">" . $ile . "</span>";
                            }

                            $submenu[] = array("link" => request::actionUrl(array("act" => "ordersadmin")), "name" => lang::txt('nagla_menu_orders_arch') . "Lista zamówień", "add" => $add);
                            $submenu[] = array("link" => request::actionUrl(array("act" => "ordersadmin_stats")), "name" => lang::txt('nagla_menu_orders_stat') . "Statystyka zamówień");

                            echo $this->adminMenuEl("orders", "cart5", lang::txt('nagla_menu_shop') . "Zamówienia", "#", $submenu);
                        }
                    }

                    if (core::isMod('invoicesadmin') && user::get()->upr(23)) {

                        $submenu = array();

                        $submenu[] = array("link" => request::actionUrl(array("act" => "invoicesadmin")), "name" => "Faktury VAT");
                        $submenu[] = array("link" => request::actionUrl(array("act" => "invoicesadmin_indexk")), "name" => "Faktury korygujące");

                        echo $this->adminMenuEl("invoices", "coins", "Faktury", request::actionUrl(array("act" => "invoicesadmin")), $submenu);
                    }


                    if (core::isMod('eventsadmin') && user::get()->upr(22)) {

                        $submenu = array();

                        $submenu[] = array("link" => request::actionUrl(array("act" => "eventsadmin_types")), "name" => "Kategorie wydarzeń");
                        $submenu[] = array("link" => request::actionUrl(array("act" => "eventsadmin")), "name" => "Wszystkie wydarzenia");
                        $submenu[] = array("link" => request::actionUrl(array("act" => "eventsadmin_add")), "name" => "Dodaj wydarzenie");

                        echo $this->adminMenuEl("events", "calendar2", "Wydarzenia", request::actionUrl(array("act" => "eventsadmin")), $submenu);
                    }

                    if (user::get()->mainAdmin() && core::isMod('cfgadmin')) {
                        echo $this->adminMenuEl("cfgadmin", "cog7", lang::txt('nagla_menu_adm_konf'), request::actionUrl(array("act" => "cfgadmin_edit")));
                    }

                    if (core::isMod('dictonaryadmin') && user::get()->upr(5)) {

                        $submenu = array();

                        cfg::loadFile(cfg::getData('mod_kat') . "dictonaryadmin/conf.php");
                        $tab = cfg::getData('dictonary_cfg', 'cat_tab');

                        foreach ($tab as $key3 => $val3) {
                            $submenu[] = array("link" => request::actionUrl(array("act" => "dictonaryadmin", "id_cat" => $key3)), "name" => $val3['name']);
                        }

                        $submenu[] = array("link" => "", "name" => "");
                        $submenu[] = array("link" => request::actionUrl(array("act" => "dictonaryadmin_types")), "name" => "Kategorie danych");

                        echo $this->adminMenuEl("dictonary", "grid7", lang::txt('nagla_menu_adm_vocabulary'), "#", $submenu);
                    }

                    if (core::isMod('commentadmin') && user::get()->upr(12)) {

                        $submenu = array();

                        cfg::loadFile(cfg::getData('mod_kat') . "comment/conf.php");
                        $tab = cfg::getData('comment_cfg', 'cat_tab');

                        foreach ($tab as $key3 => $val3) {
                            $submenu[] = array("link" => request::actionUrl(array("act" => "commentadmin", "id_cat" => $key3)), "name" => $val3['name']);
                        }

                        echo $this->adminMenuEl("comment", "bubble9", "Komentarze", "#", $submenu);
                    }

                    if (core::isMod('forum') && user::get()->upr(19)) {
                        echo $this->adminMenuEl("forum", "bubbles2", "Forum", request::actionUrl(array("act" => "forumadmin")));
                    }

                    if (core::isMod('galleryadmin') && user::get()->upr(4)) {

                        $submenu = array();

                        cfg::loadFile(cfg::getData('mod_kat') . "galleryadmin/conf.php");
                        $tab = cfg::getData('gallery_cfg', 'cat_tab');

                        foreach ($tab as $key3 => $val3) {
                            $submenu[] = array("link" => request::actionUrl(array("act" => "galleryadmin", "id_cat" => $key3)), "name" => $val3['name']);
                        }

                        echo $this->adminMenuEl("gallery", "images2", "Galerie zdjęć", "#", $submenu);
                    }

                    if (user::get()->adminU() && user::get()->adminLogi()) {
                        echo $this->adminMenuEl("uadmin-history", "list-unordered", "Logs", request::actionUrl(array("act" => "logs")));
                    }

                    if (user::get()->mainAdmin() && core::isMod('cfgadmin')) {

                        $submenu = array();

                        $submenu[] = array("link" => request::actionUrl(array("act" => "users_sys")), "name" => "System params");
                        $submenu[] = array("link" => request::actionUrl(array("act" => "sys_phpinfo")), "name" => "Server config");
                        $submenu[] = array("link" => request::actionUrl(array("act" => "cfgadmin_database")), "name" => lang::txt('nagla_menu_adm_database'));

                        if (cfg::getData('cache')) {
                            $submenu[] = array("link" => request::actionUrl(array("act" => "sys_clearcache")), "name" => lang::txt('nagla_menu_adm_clearcache'));
                        }

                        echo $this->adminMenuEl("sys", "cog3", "Technical info", "#", $submenu);
                    }

                    echo "<li class=\"nav-item-header\"><div class=\"text-uppercase fs-sm lh-sm opacity-50 sidebar-resize-hide\">HELP</div> <i class=\"ph-dots-three sidebar-resize-show\" title=\"\" data-original-title=\"HELP\"></i></li>";

                    $submenu = [];
                    echo $this->adminMenuEl("help", "help", "Help", request::actionUrl(array("act" => "help")), $submenu);

                    $count_new = core::sql()->count("s.id", " FROM " . cfg::getData("sql_tab", "suggestionbox") . " s LEFT JOIN " . cfg::getData("sql_tab", "suggestionbox_viewed") . " v ON v.id_s = s.id WHERE "
                        . "v.id_u = '" . user::get()->id() . "' AND s.editor_date > v.last_viewed");

                    echo $this->adminMenuEl("suggestionbox", "box", "Suggestion box " . ($count_new > 0 ? "<span class=\"badge badge-info\">" . $count_new . "</span>" : ""), request::actionUrl(array("act" => "suggestionbox")), $submenu);
                    ?>
        <?php
                }
            }

            public function headingPanel()
            {
        ?><div class="header-elements d-none">
            <div class="d-flex justify-content-center">
                <?php
                if (user::get()->logged()) {
                ?>
                    <a href="<?php echo request::actionUrl(array("act" => "users_panel")); ?>" class="btn btn-link btn-float text-white"><i class="icon-home"></i><span><?php echo lang::txt('nagla_pan'); ?></span></a>
                <?php
                }
                if (cfg::getData('project_type') != 'app') {
                ?>
                    <a href="<?php
                                if (lang::getLang() == cfg::getData('lang_default')) {
                                    echo request::makeUrl(cfg::getData('path'));
                                } else {
                                    echo request::routeUrl("lang", array("langp" => lang::getLangR()), false);
                                }
                                ?>" class="btn btn-link btn-float text-white"><i class="icon-eye"></i><span><?php echo lang::txt('nagla_podglad'); ?></span></a>
                <?php
                }
                ?>
            </div>
        </div><?php
            }

            public function pageTitle()
            {
                ?>
        <div class="page-header-content header-elements-md-inline">

            <div class="page-title d-flex">
                <h4><i class="icon-cube mr-2"></i> <span class="font-weight-semibold"><?php echo cfg::getData('module_name'); ?></span> <?php
                                                                                                                                        if (cfg::getData('method_name')) {
                                                                                                                                            echo " - " . cfg::getData('method_name');
                                                                                                                                        }
                                                                                                                                        ?></h4>
                <a href="#" class="header-elements-toggle text-white d-md-none"><i class="icon-more"></i></a>
            </div>

        </div>
        <?php
            }

            public function breadcrumbLine($tpl = "", $separator = "")
            {

                if (!$tpl) {
                    $tpl = cfg::$data['tpl_kat'] . "breadcrumb/admin.php";
                }

                $tab = cfg::getData('breadcrumb');

                $url = "";

                reset($tab);
                foreach ($tab as $key => $val) {

                    if (!empty($val['url'])) {
                        $url .= "<a class=\"breadcrumb-item\" href=\"" . $val['url'] . "\"";
                        if (!empty($val['target'])) {
                            $url .= " target=\"" . $val['target'] . "\"";
                        }
                        $url .= ">" . $val['anchor'] . "</a>";
                    } else {
                        $url .= "<span class=\"breadcrumb-item active\">" . $val['anchor'] . "</span>";
                    }
                }

                if ($url) {
                    $view = new view($tpl);
                    $view->url = $url;
                    $view->display();
                }
            }

            /**
             * content language menu
             */
            public function langMenu()
            {

                if (user::get()->logged()) {

                    $tab_lang = cfg::getData('tab_lang');

                    if ($tab_lang && is_array($tab_lang) && count($tab_lang) > 1) {
        ?>
                <li class="nav-item dropdown">
                    <a href class="navbar-nav-link align-items-center dropdown-toggle " data-bs-toggle="dropdown"><img class="px-1" src="public/admin/images/flags/<?php echo lang::getLangR(); ?>.png" alt=""><span class="d-none d-lg-inline-block mx-lg-2"><?php echo lang::txt('lang_name_' . lang::getLangR()); ?></span><span class="caret"></span></a>
                    <div class="dropdown-menu dropdown-menu-end" data-bs-popper="static">
                        <?php
                        foreach ($tab_lang as $key => $val) {
                            echo "<a class=\"dropdown-item\" href=\"" . request::actionUrl(array('act' => "users_panel", "langp" => $val), false) . "\"><img class='px-1' src=\"public/admin/images/flags/" . $val . ".png\" alt=\"" . $val . "\"> " . lang::txt('lang_name_' . $val) . "</a>";
                        }
                        ?>
                    </div>
                </li>

            <?php
                    }
                }
            }

            public function loggedStatus()
            {

                if (user::get()->logged()) {
            ?><span class="badge bg-success ml-md-3 mr-md-auto">Online</span><?php
                                                                            } else {
                                                                                ?><span class="badge bg-danger ml-md-3 mr-md-auto">not logged in</span><?php
                                                                                                                                                    }
                                                                                                                                                }

                                                                                                                                                public function usersMenu()
                                                                                                                                                {
                                                                                                                                                    if (user::get()->logged()) {
                                                                                                                                                        ?><li class="nav-item dropdown user">
                <a href="#" class="navbar-nav-link d-flex align-items-center dropdown-toggle" data-bs-toggle="dropdown">
                    <img class="rounded-circle user-avatar" src="<?php
                                                                                                                                                        if (user::get()->getData('img3_name') && cfg::getData('u_cfg', 'img')) {
                                                                                                                                                            echo cfg::getData('u_cfg', 'kat') . user::get()->getData('img3_name');
                                                                                                                                                        } else {
                                                                                                                                                            echo "public/admin/images/placeholder.jpg";
                                                                                                                                                        };
                                                                    ?>" alt="" />
                    <span class="user-name d-none d-lg-inline-block mx-lg-2"><?php echo txt::substr(user::get()->name(), 0, 40); ?></span>
                </a>
                <div class="dropdown-menu dropdown-menu-end " data-bs-popper="static">
                    <a class="dropdown-item" href="<?php echo request::actionUrl(array('act' => "users_logoutadmin")); ?>"><i class="icon-switch2"></i> Logout</a>
                </div>
            </li><?php
                                                                                                                                                    }
                                                                                                                                                }

                                                                                                                                                public function messagesPanel()
                                                                                                                                                {
                                                                                                                                                    if (user::get()->logged() && core::isMod('mail')) {
                                                                                                                                                        $ile = core::sql()->count("id", " FROM " . cfg::getData('sql_tab', 'mail') . " WHERE typ=1 AND id_odb='" . user::get()->id() . "' AND status=1");

                                                                                                                                                        $query = " FROM " . cfg::getData('sql_tab', 'mail') . " p ";
                                                                                                                                                        $query .= " LEFT JOIN " . cfg::getData('sql_tab', 'users') . " u ON p.id_odb=u.id ";
                                                                                                                                                        $query .= " WHERE p.id_odb='" . user::get()->id() . "' AND p.typ=1 AND p.status!=4 ";
                                                                                                                                                        $query .= " ORDER BY p.odb DESC, p.id_odb DESC LIMIT 0,5";
                                                                                                                                                        $tab = core::sql()->getRows("SELECT p.*, u.img3_name, u.img3_w, u.img3_h, u.imie, u.nazwisko, u.login, u.email " . $query); ?><li class="nav-item dropdown">
                <a href="#" class="navbar-nav-link dropdown-toggle caret-0" data-toggle="dropdown">
                    <i class="icon-bubble8"></i>
                    <span class="d-md-none ml-2">Wiadomości</span><?php
                                                                                                                                                        if ($ile) {
                                                                                                                                                            echo "<span class=\"badge badge-pill bg-warning-400 ml-auto ml-md-0\">" . $ile . "</span>";
                                                                                                                                                        }
                                                                    ?>
                </a>
                <div class="dropdown-menu dropdown-menu-right dropdown-content wmin-md-350">
                    <div class="dropdown-content-header">
                        <span class="font-weight-semibold">Wiadomości</span>
                        <a href="<?php echo request::actionUrl(array('act' => "mail_message")); ?>" class="text-default"><i class="icon-compose"></i></a>
                    </div>

                    <?php
                                                                                                                                                        if (!empty($tab)) {
                    ?><div class="dropdown-content-body dropdown-scrollable dropdown-message">
                            <ul class="media-list"><?php
                                                                                                                                                            foreach ($tab as $key => $val) {

                                                                                                                                                                echo "<li class=\"media\">";
                                                                                                                                                                echo "<div class=\"mr-3 position-relative\"><img src=\"public/admin/images/placeholder.jpg\" class=\"rounded-circle\" alt=\"\"></div>";
                                                                                                                                                                echo "<div class=\"media-body\"><div class=\"media-title\">";
                                                                                                                                                                echo "	<a href=\"" . request::actionUrl(array('act' => "mail_odb", "id" => $val['id'])) . "\">";
                                                                                                                                                                echo "		<span class=\"font-weight-semibold\">" . user::get()->name($val) . "</span>";
                                                                                                                                                                echo "		<span class=\"text-muted float-right font-size-sm\">";
                                                                                                                                                                if (date::sysToUser($val['data_wys'], "Y-m-d") == date::userDate('Y-m-d')) {
                                                                                                                                                                    echo date::sysTouser($val['data_wys'], "G:i");
                                                                                                                                                                } else {
                                                                                                                                                                    echo date::sysTouser($val['data_wys'], cfg::getData('admin_short_date'));
                                                                                                                                                                }
                                                                                                                                                                echo "</span>";
                                                                                                                                                                echo "</a></div>";
                                                                                                                                                                echo "<span class=\"text-muted\">";
                                                                                                                                                                echo txt::substr($val['name'], 0, 60);
                                                                                                                                                                if (txt::strlen($val['name']) > 60) {
                                                                                                                                                                    echo "...";
                                                                                                                                                                }
                                                                                                                                                                echo "</span>";
                                                                                                                                                                echo "</div>";
                                                                                                                                                                echo "</li>";
                                                                                                                                                            }
                                                    ?></ul>
                        </div><?php
                                                                                                                                                        } else {
                                                                                                                                                            echo "<div class=\"center\">Brak wiadomości</div>";
                                                                                                                                                        }
                                ?>

                    <div class="dropdown-content-footer bg-light">
                        <a class="text-grey mr-auto" href="<?php echo request::actionUrl(array('act' => "mail_odb")); ?>" data-popup="tooltip" title="Wszystkie wiadomości"><i class="icon-menu d-block"></i></a>
                    </div>
                </div>
            </li><?php
                                                                                                                                                    }
                                                                                                                                                }

                                                                                                                                                public function logsPanel()
                                                                                                                                                {

                                                                                                                                                    if (user::get()->adminLogi()) {
                    ?><li class="nav-item dropdown">

                <a href="#" class="navbar-nav-link dropdown-toggle caret-0" data-toggle="dropdown">
                    <i class="icon-puzzle3"></i>
                    <span class="d-md-none ml-2">Logi systemowe</span>
                    <?php
                                                                                                                                                        $ile = core::sql()->count("id", " FROM " . cfg::getData('sql_tab', 'logs') . " WHERE idtf='b_log' AND kiedy>'" . date::move(60) . "'");
                                                                                                                                                        if ($ile) {
                                                                                                                                                            echo "<span class=\"badge badge-pill bg-warning-400 ml-auto ml-md-0\">" . $ile . "</span>";
                                                                                                                                                        }
                    ?>
                </a>


                <div class="dropdown-menu dropdown-content wmin-md-350">

                    <div class="dropdown-content-header">
                        Logs history
                    </div>

                    <div class="dropdown-content-body dropdown-scrollable">
                        <ul class="media-list">

                            <?php
                                                                                                                                                        $dane_tab = core::sql()->getRows("SELECT * FROM " . cfg::getData('sql_tab', 'logs') . " ORDER BY id DESC LIMIT 0,5");

                                                                                                                                                        if (!empty($dane_tab)) {
                                                                                                                                                            foreach ($dane_tab as $key => $val) {
                            ?><li class="media">
                                        <div class="mr-3">
                                            <a href="#" class="btn bg-transparent <?php
                                                                                                                                                                if ($val['act'] == 'users_loginadmin2' || $val['act'] == 'users_login2') {
                                                                                                                                                                    echo "border-success  text-success";
                                                                                                                                                                } else if ($val['act'] == 'users_logoutadmin' || $val['act'] == 'users_logout') {
                                                                                                                                                                    echo "border-warning text-warning";
                                                                                                                                                                } else {
                                                                                                                                                                    echo "border-info text-info";
                                                                                                                                                                }
                                                                                    ?>
                                               rounded-round border-2 btn-icon"><i class="icon-git-merge"></i></a>
                                        </div>

                                        <div class="media-body">
                                            <?php
                                                                                                                                                                echo "<a href=\"" . user::get()->urlo(array("act" => "uadmin_logiarch", "id_u" => $val['id_u'])) . "\"> " . $val['login'] . "</a> ";
                                                                                                                                                                echo $val['opis'];
                                                                                                                                                                echo "<div class=\"text-muted font-size-sm\">" . date::sysTouser($val['kiedy'], cfg::getData('admin_long_date')) . "</div>";
                                            ?>
                                        </div>
                                    </li><?php
                                                                                                                                                            }
                                                                                                                                                        }
                                            ?>
                        </ul>
                    </div>

                    <div class="dropdown-content-footer bg-light">
                        <a class="text-grey center" href="<?php echo request::actionUrl(array('act' => "uadmin_logiarch")); ?>" data-popup="tooltip" title="All logs"><i class="icon-menu d-block"></i></a>
                    </div>
                </div>
            </li>
<?php
                                                                                                                                                    }
                                                                                                                                                }

                                                                                                                                                public function checkSidebar()
                                                                                                                                                {

                                                                                                                                                    if (user::get()->upr(11) && core::getLayout() == "admin") {
                                                                                                                                                        return true;
                                                                                                                                                    }

                                                                                                                                                    if (user::get()->upr(20) && core::getLayout() == "shopadmin") {
                                                                                                                                                        return true;
                                                                                                                                                    }

                                                                                                                                                    if (user::get()->upr(19) && core::getLayout() == "forumadmin") {
                                                                                                                                                        return true;
                                                                                                                                                    }

                                                                                                                                                    return false;
                                                                                                                                                }

                                                                                                                                                public function getBgImg()
                                                                                                                                                {

                                                                                                                                                    return "public/admin/images/backgrounds/" . rand(1, 10) . ".jpg";
                                                                                                                                                }

                                                                                                                                                public function __construct()
                                                                                                                                                {

                                                                                                                                                    lang::load('head_admin', 2);

                                                                                                                                                    // STYLES
                                                                                                                                                    cfg::setCss("public/assets/fonts/inter/inter.css");
                                                                                                                                                    cfg::setCss("public/assets/icons/phosphor/styles.min.css");
                                                                                                                                                    cfg::setCss("public/admin/css/all.min.css");
                                                                                                                                                    cfg::setCss("public/admin/css/bootstrap_limitless.min.css");
                                                                                                                                                    cfg::setCss("public/admin/css/bootstrap.min.css");
                                                                                                                                                    cfg::setCss("public/admin/css/components.min.css");
                                                                                                                                                    cfg::setCss("public/admin/css/layout.min.css");
                                                                                                                                                    cfg::setCss("public/admin/css/styles.min.css");
                                                                                                                                                    cfg::setCss("public/admin/css/plasticon_styles.min.css");
                                                                                                                                                    cfg::setCss("public/admin/css/guide.min.css");

                                                                                                                                                    // JavaScript
                                                                                                                                                    cfg::setJs("public/assets/js/bootstrap/bootstrap.bundle.min.js");
                                                                                                                                                    cfg::setJs("public/assets/js/jquery/jquery.min.js");
                                                                                                                                                    cfg::setJs("public/assets/js/vendor/tables/datatables/datatables.min.js");
                                                                                                                                                    cfg::setJs("public/assets/js/vendor/forms/selects/select2.min.js");
                                                                                                                                                    cfg::setJs("public/assets/js/app.js");
                                                                                                                                                    cfg::setJs("public/assets/js/js.cookie.js");
                                                                                                                                                    cfg::setJs("public/assets/js/cookie.js");
                                                                                                                                                    // cfg::setJs("public/admin/js/app.js");

                                                                                                                                                    cfg::setJs("public/admin/js/admin.js");
                                                                                                                                                    // cfg::setJs("public/admin/js/sweet_alert.min.js");
                                                                                                                                                    cfg::setJs("public/admin/js/quick_actions.js");
                                                                                                                                                    cfg::setJs("public/admin/js/class/conf.js");
                                                                                                                                                    cfg::setJs("public/admin/js/class/menuState.js");
                                                                                                                                                    cfg::setJs("public/admin/js/darkMode.js", ['defer' => true]);
                                                                                                                                                    cfg::setJs("public/admin/js/plasticon_index.js", ['defer' => true]);
                                                                                                                                                    cfg::setJs("public/admin/js/i18n/" . lang::getLangR2() . ".js");
                                                                                                                                                    cfg::setJs("https://cdn.jsdelivr.net/npm/sweetalert2@11");
                                                                                                                                                }
                                                                                                                                            }
?>
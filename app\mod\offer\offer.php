<?php
if (!defined('DS')) {
    exit();
}

require_once SYSROOT . '/lib/mpdf/autoload.php';
require_once "pdfpartials/test.php";


class Offer extends modulegce
{
    private $port, $name, $mpdf;
    private $offerId;
    private $gap, $sheetGap, $letterGap, $mTop, $mLeft, $mRight, $mBottom, $sheetMarginLeft, $sheetMarginRight, $primaryColor;
    private $primaryFontSize, $h2FontSize, $h3FontSize, $pt8FontSize, $primaryTableBorder;
    private $client, $project, $yourReference, $ourReference;
    private $clientName, $clientPhone, $clientEmail, $clientAddress, $clientCity, $clientPostCode, $clientCountry, $documentName, $coverLetter;
    private $pricingIncoterms, $pricingTable, $pricingTotal, $pricingTotalQty, $pricingTotalCurrency;
    private $technicalGeneralList, $executionDescription, $technicalEquipmentList;
    private $documentationLanguage, $documentationCopies, $documentationAccording, $documentationIncluding;
    private $svg;
    private $conditionsTerms, $conditionsConditions, $conditionsNotes, $conditionsSlidingPrice, $conditionsSaleAndService, $conditionsLimitationLiability, $conditionsGeneralNotes;
    private $generalRemarks;
    private $tableOfContents = [];

    private $salesName, $salesEmail, $salesPhone, $technicalName, $technicalEmail, $technicalPhone;
    private $city, $date;

    private $technicalItems;

    private $sections, $sectionsView;

    private $appName,$appVersion;
    

    public function __construct()
    {
        $defaultConfig = (new \Mpdf\Config\ConfigVariables())->getDefaults();
        $fontDirs = $defaultConfig['fontDir'];

        $defaultFontConfig = (new \Mpdf\Config\FontVariables())->getDefaults();
        $fontData = $defaultFontConfig['fontdata'];

        $this->appName = "offer";
        $this->appVersion = "0.5.1";

        $this->name = 'offer';
        $this->port = 4444;

        $this->offerId = 1;

        $this->gap = 12.5;
        $this->sheetGap = $this->gap * 2;
        $this->letterGap = $this->gap / 6;

        $this->mTop = $this->gap;
        $this->mBottom = $this->gap;

        $this->mLeft = 0;
        $this->mRight = 0;

        $this->sheetMarginLeft = $this->sheetGap;
        $this->sheetMarginRight = $this->sheetGap;

        $this->primaryTableBorder = 'border: 1px solid rgb(191,191,191)';

        $this->primaryColor = '#004AAD';

        $this->client = "Company XYZ";
        $this->project = "HCl Tank DN3000";
        $this->yourReference = "1--------1";
        $this->ourReference = "25700000";

        $this->clientName = "TEST DEMO";
        $this->clientPhone = "************";
        $this->clientEmail = "<EMAIL>";
        $this->clientAddress = "123 Main Street";
        $this->clientCity = "New York";
        $this->clientPostCode = "10001";
        $this->clientCountry = "United States";

        $this->salesName = "John Doe";
        $this->salesEmail = "<EMAIL>";
        $this->salesPhone = "************";

        $this->technicalName = "Jane Doe";
        $this->technicalEmail = "<EMAIL>";
        $this->technicalPhone = "************";

        $this->date = date("d.m.Y");
        $this->city = "Toruń";

        $this->documentName = "Budget Offer";
        $this->coverLetter = '
        <p>The standard Lorem Ipsum passage, used since the 1500s</p>

        <p>"Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor incididunt ut labore et dolore magna aliqua. Ut enim ad minim veniam, quis nostrud exercitation ullamco laboris nisi ut aliquip ex ea commodo consequat. Duis aute irure dolor in reprehenderit in voluptate velit esse cillum dolore eu fugiat nulla pariatur. Excepteur sint occaecat cupidatat non proident, sunt in culpa qui officia deserunt mollit anim id est laborum."</p>

        <p>Section 1.10.32 of "de Finibus Bonorum et Malorum", written by Cicero in 45 BC</p>

        <p>"Sed ut perspiciatis unde omnis iste natus error sit voluptatem accusantium doloremque laudantium, totam rem aperiam, eaque ipsa quae ab illo inventore veritatis et quasi architecto beatae vitae dicta sunt explicabo. Nemo enim ipsam voluptatem quia voluptas sit aspernatur aut odit aut fugit, sed quia consequuntur magni dolores eos qui ratione voluptatem sequi nesciunt. Neque porro quisquam est, qui dolorem ipsum quia dolor sit amet, consectetur, adipisci velit, sed quia non numquam eius modi tempora incidunt ut labore et dolore magnam aliquam quaerat voluptatem. Ut enim ad minima veniam, quis nostrum exercitationem ullam corporis suscipit laboriosam, nisi ut aliquid ex ea commodi consequatur? Quis autem vel eum iure reprehenderit qui in ea voluptate velit esse quam nihil molestiae consequatur, vel illum qui dolorem eum fugiat quo voluptas nulla pariatur?"</p>

        <p>1914 translation by H. Rackham</p>

        <p>"But I must explain to you how all this mistaken idea of denouncing pleasure and praising pain was born and I will give you a complete account of the system, and expound the actual teachings of the great explorer of the truth, the master-builder of human happiness. No one rejects, dislikes, or avoids pleasure itself, because it is pleasure, but because those who do not know how to pursue pleasure rationally encounter consequences that are extremely painful. Nor again is there anyone who loves or pursues or desires to obtain pain of itself, because it is pain, but because occasionally circumstances occur in which toil and pain can procure him some great pleasure. To take a trivial example, which of us ever undertakes laborious physical exercise, except to obtain some advantage from it? But who has any right to find fault with a man who chooses to enjoy a pleasure that has no annoying consequences, or one who avoids a pain that produces no resultant pleasure?"</p>

        <p>Section 1.10.33 of "de Finibus Bonorum et Malorum", written by Cicero in 45 BC</p>

        <p>"At vero eos et accusamus et iusto odio dignissimos ducimus qui blanditiis praesentium voluptatum deleniti atque corrupti quos dolores et quas molestias excepturi sint occaecati cupiditate non provident, similique sunt in culpa qui officia deserunt mollitia animi, id est laborum et dolorum fuga. Et harum quidem rerum facilis est et expedita distinctio. Nam libero tempore, cum soluta nobis est eligendi optio cumque nihil impedit quo minus id quod maxime placeat facere possimus, omnis voluptas assumenda est, omnis dolor repellendus. Temporibus autem quibusdam et aut officiis debitis aut rerum necessitatibus saepe eveniet ut et voluptates repudiandae sint et molestiae non recusandae. Itaque earum rerum hic tenetur a sapiente delectus, ut aut reiciendis voluptatibus maiores alias consequatur aut perferendis doloribus asperiores repellat."</p>

        <p>1914 translation by H. Rackham</p>

        <p>"On the other hand, we denounce with righteous indignation and dislike men who are so beguiled and demoralized by the charms of pleasure of the moment, so blinded by desire, that they cannot foresee the pain and trouble that are bound to ensue; and equal blame belongs to those who fail in their duty through weakness of will, which is the same as saying through shrinking from toil and pain. These cases are perfectly simple and easy to distinguish. In a free hour, when our power of choice is untrammelled and when nothing prevents our being able to do what we like best, every pleasure is to be welcomed and every pain avoided. But in certain circumstances and owing to the claims of duty or the obligations of business it will frequently occur that pleasures have to be repudiated and annoyances accepted. The wise man therefore always holds in these matters to this principle of selection: he rejects pleasures to secure other greater pleasures, or else he endures pains to avoid worse pains."</p>';

        // 🙀TODO:  Moim zdaniem trzeba przedyskutować sposób przechowywania danych poszczególnych sekcji.
        //          Musielibyśmy sprawdzić dwie rzeczy: w jakich strukturach będą przychodzić tutaj dane
        //          z innych systemów i jakie sekcje i subsekcje są pomijane w przypadku różnych projektów.
        //          Ewentualnie mówimy Fuck It i będziemy pisać parsery danych 😸

        // 😺 Pricing data
        $this->pricingIncoterms = "FCA Toruń";
        $this->pricingTable = [
            (object)['number' => 1, 'name' => 'Tank', 'qty' => 1, 'ppa' => '10 000,55', 'total' => '10 000,55', 'currency' => '€'],
            (object)['number' => 2, 'name' => 'Transport', 'qty' => 1, 'ppa' => '5 300,45', 'total' => '5 300,45', 'currency' => '€'],
        ];
        $this->pricingTotal = '15 301,00';
        $this->pricingTotalQty = 1;
        $this->pricingTotalCurrency = '€';



        // 😺 Documentation Data
        $this->documentationLanguage = "English";
        $this->documentationCopies = 1;
        $this->documentationAccording = "EN10204 - 3.1 (not for components from sub-suppliers for which a 2.2 certificate is provided)";
        $this->documentationIncluding = [
            "Conformity certificate",
            "Warranty",
            "Material Certificates",
            "Strength Calculation (if mentioned above)",
            "Company Qualifications",
            "Staff Qualifications",
            "Welding/Lamination review",
            "Barcol Hardness test",
            "Thicknesses check",
            "Main dimensions check",

        ];

        // 😺 Commercial conditions Data
        $this->conditionsTerms = [
            "Invoicing" => [
                "40% at order confirmation",
                "55% monthly progress payment",
                "5% after documentation"
            ],
            "Payment" => [
                "30 days net from date of invoice",
            ],
            "Prices" => [
                "All prices excluding VAT / withholding tax"
            ],
            "Warranty" => ["24 months warranty after commissioning", " max. 36 months after delivery"],
            "Validity quotation" => ["10 days"],
            "notes" => "However, even if the offer is accepted within the aforementioned period and without prejudice to further rights, we are entitled to increase the price unilaterally in relation to the increase in price-forming factors, as exemplified below, provided that the agreed delivery date is postponed at your request or for other reasons not falling within our sphere of responsibility, and, in the period between acceptance of the offer and the time of delivery, price-forming factors, in particular commodity prices, wage costs and ancillary wage costs, procurement costs and/or production costs increase. Other price adjustment options, in particular within our General Terms and Conditions, remain unaffected unless this is in contradiction to the above regulation."

        ];

        // 😹 Conditions
        $this->conditionsConditions = "
            <p>Sliding price clause</p>
            <p>
            Due to the dynamic development on the raw material and logistics market (shortage and therefore related cost development), we see ourselves forced to forward cost increases, especially for raw materials and transports, to our clients. 
            If these prices increase at the time of execution compared to the day of our offer by more than 5%, the invoicing price will be billed acc. to the following sliding price formula:  P = P0 * (a + r*Rm/R0) whereby:</p>";
        $this->conditionsNotes = "Condition Notes";
        $this->conditionsSlidingPrice = "conditions Sliding Price";
        $this->conditionsSaleAndService = "conditions Sale And Service";
        $this->conditionsLimitationLiability = "conditions Limitation Liability";
        $this->conditionsGeneralNotes = "conditions General Notes";



        $this->generalRemarks = "general Remarks";




        $this->technicalItems = [
            (object) [
                "name" => "Item 1",
                "general" => [
                    "Expert approval: not considered",
                    "Strength calculations: without calculation",
                    "Tolerances: According EN 13121-3",
                    "Installation: in the building",
                    "Earthquake: not considered",
                    "Additional loads: not considered",
                    "Leakage test: Test acc. EN13121-3 at ambient temperature",
                    "Railing/ladder/platform: not considered",
                    "Foundation (only for tanks): EN 13121-4 § 5.3.2",
                ],
                "executionDescription" => "The tank will be executed according to received specification. The design includes flat bottom and dished top.",
                'equipment' => [
                    "Nameplate: According to Plasticon standard ",
                    "Gaskets: Not included ",
                    "Screws / Nuts: A2/A4 (only for manholes and covered nozzles) ",
                    "Lifting lugs: 1.4301 ",
                    "Anchor brackets: GRP ",
                    "Packaging: all the nozzles will be protected ",
                ],
                "img" => null,
                "svg" => "<svg xmlns='http://www.w3.org/2000/svg' width='350' height='350' viewBox='0 0 350 350'>
                        <!-- Body -->
                        <ellipse cx='200' cy='220' rx='120' ry='100' fill='#000000' />
                        
                        <!-- Head -->
                        <circle cx='200' cy='150' r='80' fill='#808080' />
                        
                        <!-- Ears -->
                        <path d='M140 90 L120 40 L160 90 Z' fill='#808080' />
                        <path d='M260 90 L280 40 L240 90 Z' fill='#808080' />
                        <path d='M145 85 L135 45 L160 85 Z' fill='#FFC0CB' />
                        <path d='M255 85 L265 45 L240 85 Z' fill='#FFC0CB' />
                        
                        <!-- Eyes -->
                        <circle cx='160' cy='140' r='15' fill='#FFFFFF' />
                        <circle cx='240' cy='140' r='15' fill='#FFFFFF' />
                        <circle cx='165' cy='140' r='8' fill='#000000' />
                        <circle cx='245' cy='140' r='8' fill='#000000' />
                        <circle cx='163' cy='137' r='3' fill='#FFFFFF' />
                        <circle cx='243' cy='137' r='3' fill='#FFFFFF' />
                        
                        <!-- Nose -->
                        <path d='M195 160 L205 160 L200 170 Z' fill='#FFC0CB' />
                        
                        <!-- Mouth -->
                        <path d='M180 180 Q200 190 220 180' fill='none' stroke='#000000' stroke-width='2' />
                        
                        <!-- Whiskers -->
                        <line x1='140' y1='170' x2='80' y2='160' stroke='#000000' stroke-width='2' />
                        <line x1='140' y1='175' x2='80' y2='175' stroke='#000000' stroke-width='2' />
                        <line x1='140' y1='180' x2='80' y2='190' stroke='#000000' stroke-width='2' />
                        
                        <line x1='260' y1='170' x2='320' y2='160' stroke='#000000' stroke-width='2' />
                        <line x1='260' y1='175' x2='320' y2='175' stroke='#000000' stroke-width='2' />
                        <line x1='260' y1='180' x2='320' y2='190' stroke='#000000' stroke-width='2' />
                        
                        <!-- Paws -->
                        <ellipse cx='150' cy='310' rx='30' ry='20' fill='#808080' />
                        <ellipse cx='250' cy='310' rx='30' ry='20' fill='#808080' />
                        
                        <!-- Tail -->
                        <path d='M300 250 Q330 230 350 260' fill='none' stroke='#808080' stroke-width='40' stroke-linecap='round' />
                    </svg>",
                'medium' => 'HCl',
                'density' => '1300',
                'densityUnit' => 'kg/m³',

                'pressureOperatingValue' => '0.5',
                'pressureOperatingUnit' => 'Barg',
                'pressureDesignValue' => '0.8',
                'pressureDesignUnit' => 'Barg',

                'vaccumOperatingValue' => '-',
                'vaccumOperatingUnit' => 'Barg',
                'vaccumDesignValue' => '-',
                'vaccumDesignUnit' => 'Barg',

                'temperatureOperatingValue' => '26,96',
                'temperatureOperatingUnit' => 'Barg',
                'temperatureDesignValue' => '26,96',
                'temperatureDesignUnit' => 'Barg',

                'totalVolumeOperatingValue' => '26,96',
                'totalVolumeOperatingUnit' => 'm³',
                'totalVolumeDesignValue' => '26,96',
                'totalVolumeDesignUnit' => 'm³',

                'usefulVolumeOperatingValue' => '26,96',
                'usefulVolumeOperatingUnit' => 'm³',
                'usefulVolumeDesignValue' => '26,96',
                'usefulVolumeDesignUnit' => 'm³',

                'diameterOperatingValue' => '26,96',
                'diameterOperatingUnit' => 'mm',
                'diameterDesignValue' => '26,96',
                'diameterDesignUnit' => 'mm',

                'cylindricalHeightOperatingValue' => '26,96',
                'cylindricalHeightOperatingUnit' => 'mm',
                'cylindricalHeightDesignValue' => '26,96',
                'cylindricalHeightDesignUnit' => 'mm',

                'totalHeightOperatingValue' => '26,96',
                'totalHeightOperatingUnit' => 'mm',
                'totalHeightDesignValue' => '26,96',
                'totalHeightDesignUnit' => 'mm',

                'weightOperatingValue' => '26,96',
                'weightOperatingUnit' => 'kg',
                'weightDesignValue' => '26,96',
                'weightDesignUnit' => 'kg',

                'corrosionBarrierOperatingValue' => '2,5mm',
                'corrosionBarrierOperatingUnit' => 'Derakane 411-350',
                'corrosionBarrierDesignValue' => '2,5mm',
                'corrosionBarrierDesignUnit' => 'Derakane 411-350',

                'interlayerOperatingValue' => '2,5mm',
                'interlayerOperatingUnit' => 'Derakane 411-350',
                'interlayerDesignValue' => '2,5mm',
                'interlayerDesignUnit' => 'Derakane 411-350',

                'structuralResinOperatingValue' => '2,5mm',
                'structuralResinOperatingUnit' => 'Derakane 411-350',
                'structuralResinDesignValue' => '2,5mm',
                'structuralResinDesignUnit' => 'Derakane 411-350',

                'topcoatOperatingValue' => '2,5mm',
                'topcoatOperatingUnit' => 'Derakane 411-350',
                'topcoatDesignValue' => '2,5mm',
                'topcoatDesignUnit' => 'Derakane 411-350',

                'additionalOperatingValue' => '2,5mm',
                'additionalOperatingUnit' => 'Derakane 411-350',
                'additionalDesignValue' => '2,5mm',
                'additionalDesignUnit' => 'Derakane 411-350',


                'notesLine1' => 'Nozzle fabrication according to DIN 16966 Part 6, drilling according to DIN 1092-1.',
                'notesLine2' => 'All nozzles are protected with flange protection caps to prevent damage.',

                'miscTable' => [
                    (object)[
                        'dn' => '25',
                        'description' => 'Fixed flange',
                        'quantity' => '1',
                        'equipment' => 'not considered',
                    ],
                ],

                'exclusions' => 'Transport, installation, heating, insulation, internals, elements not mentioned above, other gaskets and fasteners, post-curing.'
            ],

        ];







        // 🙀 TODO: uzgodnić czy przesyłamy kwoty jako przeformatowane stringi czy floaty i reformatujemy tutaj
        $config = [
            'margin_left' => $this->sheetGap,
            'margin_right' => $this->sheetGap,
            'margin_bottom' => 30,
            'margin_top' => 40,
            'fontdata' => $fontData + [
                'poppins' => [
                    'R' => 'Poppins-Regular.ttf',
                    "B" => 'Poppins-Bold.ttf'
                ]
            ],
            'default_font' => 'poppins',
            'default_line_height' => 1.4
        ];



        $stylesheet = file_get_contents(__DIR__ . '/assets/css/pdf.style.css');

        $this->mpdf = new \Mpdf\Mpdf($config);
        $this->mpdf->WriteHTML($stylesheet, \Mpdf\HTMLParserMode::HEADER_CSS);

        // Initialize table of contents
        $this->initializeTableOfContents();
    }

    // DATABASE INITIALIZATION

    public function dbinit()
    {

        $init = offerhelper::db_init();

        if ($init['success']) {
            echo "<h3>{$init['msg']}</h3>";
        } else {
            echo "<h3 style='color: red;'>{$init['msg']}</h3>";
        }
    }

    public function migrate()
    {

        $styles = [
            'existing' => 'background-color: #f0f8ff; border: 1px solid #cce5ff; color: #004085;',
            'success' => 'background-color: #e8f5e9; border: 1px solid #c8e6c9; color: #1b5e20;',
            'error' => 'background-color: #fff3cd; border: 1px solid #ffeeba; color: #856404;',
            'critical_error' => 'background-color: #f8d7da; border: 1px solid #f5c6cb; color: #721c24;'
        ];
        $errorCount = 0;


        $dataBaseName = "offerpdf";
        $migrationTable = "$dataBaseName.migrations";

        echo '<div style="max-width: 800px; margin: 20px auto; font-family: Arial, sans-serif;">';
        echo '<h1 style="color: #333; border-bottom: 2px solid #eee; padding-bottom: 10px;">Status Migracji Bazy Danych</h1>';

        $migrationCheckSql = "CHECK TABLE table_name;";
        $migrationCheck = core::sql()->zap($migrationCheckSql);

        if ($migrationCheck === false) {
            echo '<div style="background-color: #fee; border: 1px solid #faa; padding: 15px; border-radius: 4px; margin: 10px 0;">
                    <strong style="color: #d00;">Błąd:</strong> Brak tabeli migracji
                  </div>';
            return 0;
        }

        $migrations = [];
        $files = new RecursiveIteratorIterator(
            new RecursiveDirectoryIterator(__DIR__ . "/migrations")
        );

        foreach ($files as $file) {

            if ($file->isDir() || $file->getExtension() != 'php') {
                continue;
            }
            
            
            $filePath = $file->getPathname();
            $fileName = $file->getFilename();
            $content = file_get_contents($filePath);
            
            if (preg_match('/\$migration\s*=\s*["\']([^"\']+)["\'];/', $content, $matches)) {
                $migrations[] = [
                    'file' => $fileName,
                    'value' => $matches[1]
                ];
            }else{
                echo '<div style="padding: 12px; margin: 8px 0; border-radius: 4px;' . $styles['error'] . '">';
                echo '❌ Błąd dopasowania wzorca zapytania -'.$fileName;
                echo '</div>';
            }
        }


        $dbMigrationsSql = "SELECT * from $migrationTable";
        $dbMigrations = core::sql()->getRows($dbMigrationsSql);
        $currentMigrations = [];
        foreach ($dbMigrations as $migration) {
            $currentMigrations[] = $migration['migration_name'];
        }

        

        echo '<div style="margin-top: 20px;">';

        foreach ($migrations as $migration) {
            if ($errorCount == 0) {
                echo '<div style="padding: 12px; margin: 8px 0; border-radius: 4px; ';

                if (in_array($migration['file'], $currentMigrations)) {
                    echo $styles['existing'] . '">';
                    echo '✓ Istniejąca migracja: ' . htmlspecialchars($migration['file']);
                } else {
                    echo '<div style="padding: 12px; margin: 8px 0; border-radius: 4px;';
                    echo $styles['existing'] . '">';
                    echo '⚙️ Rozpoczynam migrację: ' . htmlspecialchars($migration['file']) . ' - ';

                    core::sql()->query("USE $dataBaseName;");
                    $runMigration = core::sql()->query($migration['value']);

                    if ($runMigration === false) {
                        echo '</div><div style="padding: 12px; margin: 8px 0; border-radius: 4px;' . $styles['error'] . '">';
                        echo '❌ Błąd przetwarzania - '.$migration['value'];
                        $errorCount++;
                    } else {
                        $fileName = $migration['file'];
                        $date = date("Y-m-d H:i:s");
                        $userId = user::get()->id();
                        $updateMigrationTable = core::sql()->query(
                            "INSERT INTO $migrationTable VALUES (NULL, '$fileName', '$fileName', '$date', $userId );"
                        );

                        if ($updateMigrationTable === false) {
                            echo '</div><div style="padding: 12px; margin: 8px 0; border-radius: 4px;' . $styles['critical_error'] . '">';
                            echo '⚠️ BŁĄD KRYTYCZNY: Błąd aktualizacji tablicy migracji - wymagana ingerencja administratora!';
                            $errorCount++;
                        } else {
                            echo '✅ Sukces';
                        }
                    }
                }
                echo '</div>';
            }
        }

        echo '</div></div>';
    }




    // API

    public function echo()
    {
        $request = $this->getRequestBody();

        echo json_encode($request);
        return 1;
    }

    public function new()
    {
        $request = $this->getRequestBody();
        $payload = $request;

        // Sprawdzenie, czy $payload jest stringiem i próba jego dekodowania
        if (is_string($payload)) {
            $data = json_decode($payload);
            if (json_last_error() !== JSON_ERROR_NONE) {
                echo json_encode([
                    "success" => false,
                    "message" => "(offer.php - 1) Invalid payload JSON structure",
                ]);
                return 0;
            }
        } else {
            $data = json_decode(json_encode($payload));
            if (json_last_error() !== JSON_ERROR_NONE) {

                echo json_encode([
                    "success" => false,
                    "message" => "(offer.php - 2) Invalid payload JSON structure",
                ]);
                return 0;
            }
        }

        // Sprawdzenie czy JSON ma wymagane pola
        if (!isset($data->crmId)) {
            echo json_encode([
                "success" => false,
                "message" => "(offer.php - 3)No CRM Id in payload",
            ]);
            return 0;
        }
        if (!isset($data->crmNo)) {
            echo json_encode([
                "success" => false,
                "message" => "(offer.php - 4)No CRM No in payload",
            ]);
            return 0;
        }
        if (!isset($data->name)) {
            echo json_encode([
                "success" => false,
                "message" => "(offer.php - 5)No Name in payload",
            ]);
            return 0;
        }
        if (!isset($data->description)) {
            echo json_encode([
                "success" => false,
                "message" => "(offer.php - 6)No Description in payload",
            ]);
            return 0;
        }
        if (!isset($data->locked)) {
            echo json_encode([
                "success" => false,
                "message" => "(offer.php - 7)No Locked in payload",
            ]);
            return 0;
        }

        if (!isset($data->language)) {
            echo json_encode([
                "success" => false,
                "message" => "(offer.php - 8)No Language in payload",
            ]);
            return 0;
        }

        echo offerhelper::createNewRevision($data);
        return 0;
    }

    public function getrevision()
    {
        $request = $this->getRequestBody();
        $payload = $request;

        // Sprawdzenie, czy $payload jest stringiem i próba jego dekodowania
        if (is_string($payload)) {
            $data = json_decode($payload);
            if (json_last_error() !== JSON_ERROR_NONE) {
                echo json_encode([
                    "success" => false,
                    "message" => "(offer.php - 8)Invalid payload JSON structure",
                ]);
                return 0;
            }
        } else {
            $data = json_decode(json_encode($payload));
            if (json_last_error() !== JSON_ERROR_NONE) {

                echo json_encode([
                    "success" => false,
                    "message" => "(offer.php - 9)Invalid payload JSON structure",
                ]);
                return 0;
            }
        }

        // Sprawdzenie czy JSON ma wymagane pola
        if (!isset($data->revisionId)) {
            echo json_encode([
                "success" => false,
                "message" => "(offer.php - 10)No CRM Id in payload",
            ]);
            return 0;
        }

        echo json_encode(offerhelper::getRevisionById($data->revisionId));
        return 0;
    }

    public function getinitdata()
    {
        $request = $this->getRequestBody();
        $payload = $request;

        // Sprawdzenie, czy $payload jest stringiem i próba jego dekodowania
        if (is_string($payload)) {
            $data = json_decode($payload);
            if (json_last_error() !== JSON_ERROR_NONE) {
                echo json_encode([
                    "success" => false,
                    "message" => "(offer.php - 11)Invalid payload JSON structure",
                ]);
                return 0;
            }
        } else {
            $data = json_decode(json_encode($payload));
            if (json_last_error() !== JSON_ERROR_NONE) {

                echo json_encode([
                    "success" => false,
                    "message" => "(offer.php - 12)Invalid payload JSON structure",
                ]);
                return 0;
            }
        }

        // Sprawdzenie czy JSON ma wymagane pola
        if (!isset($data->revisionId)) {
            echo json_encode([
                "success" => false,
                "message" => "(offer.php - 13)No CRM Id in payload",
            ]);
            return 0;
        }
        if (!isset($data->crmId)) {
            echo json_encode([
                "success" => false,
                "message" => "(offer.php - 14)No CRM Id in payload",
            ]);
            return 0;
        }

        $crmData = offerhelper::getCrmData($data->crmId);
        $revisionData = offerhelper::getRevisionById($data->revisionId);

        if (!$crmData['success'] || !$revisionData['success']) {
            echo json_encode([
                "success" => false,
                "message" => "(offer.php - 15)No CRM or Revision Data",
            ]);
            return 0;
        }


        echo json_encode([
            'crm' => $crmData['data']['crm']['data'],
            'client' => $crmData['data']['client']['data'],
            'finalClient' => $crmData['data']['finalClient']['data'],
            'revision' => $revisionData['data']['revisions']['data'],
        ]);
        return 1;
    }

    public function update()
    {
        $request = $this->getRequestBody();
        $payload = $request;

        // Sprawdzenie, czy $payload jest stringiem i próba jego dekodowania
        if (is_string($payload)) {
            $data = json_decode($payload, false, 512, JSON_UNESCAPED_UNICODE);
            if (json_last_error() !== JSON_ERROR_NONE) {
                echo json_encode([
                    "success" => false,
                    "message" => "(offer.php - 16)Invalid payload JSON structure",
                ]);
                return 0;
            }
        } else {
            $data = json_decode(json_encode($payload, JSON_UNESCAPED_UNICODE));
            if (json_last_error() !== JSON_ERROR_NONE) {

                echo json_encode([
                    "success" => false,
                    "message" => "(offer.php - 17)Invalid payload JSON structure",
                ]);
                return 0;
            }
        }

        // Sprawdzenie czy JSON ma wymagane pola
        if (!isset($data->revisionId)) {
            echo json_encode([
                "success" => false,
                "message" => "(offer.php - 18)No revision id in json structure",
            ]);
            return 0;
        }
        if (!isset($data->payload)) {
            echo json_encode([
                "success" => false,
                "message" => "(offer.php - 19)No payload in json structure",
            ]);
            return 0;
        }



        $revisionData = offerhelper::getRevisionById($data->revisionId);
        if (!$revisionData['success']) {
            echo json_encode($revisionData);
            return 0;
        }
        $payloadJson = json_encode($data->payload, JSON_UNESCAPED_UNICODE);


        $offerUpdateQuery = "
            UPDATE offerpdf.offers
            SET
                offer_payload=?,
                editor_id=?,
                editor_name=?,
                editor_date=?
            WHERE id=?";

        $params = [
            $payloadJson,
            user::get()->id(),
            user::get()->author(),
            date("Y-m-d H:i:s"),
            $data->revisionId
        ];

        $offerUpdate = offerhelper::executeDatabaseQueryWithParams($offerUpdateQuery, $params);

        echo json_encode($offerUpdate);
        return 1;
    }



    public function index()
    {

        $crmId = request::getGet('crm');
        $crmData = offerhelper::getCrmData($crmId);
        


        if ($crmData === false || !$crmData['success']) {
            $view = new view(cfg::$data['mod_kat'] . "{$this->name}/tpl/error.php");
            $view->error = $crmData;
            $view->display();
            return 0;
        }
        if ($crmData['data']['crm']['row_count'] === 0) {
            $view = new view(cfg::$data['mod_kat'] . "{$this->name}/tpl/error.php");
            $view->error = ['msg' => "No CRM under this ID: $crmId"];
            $view->display();
            return 0;
        }

        $view = new view(cfg::$data['mod_kat'] . "{$this->name}/tpl/index.php");
        $view->offerData = $crmData;
        $view->revisionsData = offerhelper::getRevisionList($crmId);
        $view->display();
    }


    public function app()
    {

        $dev = dev::isDev();

        cfg::setCss('public/admin/css/bootstrap_limitless.min.css');
        cfg::setCss('public/admin/css/bootstrap.min.css');
        cfg::setCss('public/admin/css/components.css');

        if ($dev) {
            cfg::setJs("http://localhost:{$this->port}/static/js/bundle.js", ['defer' => true]);
        } else {
            cfg::setJs("public/admin/{$this->appName}_assets/{$this->appVersion}/{$this->appName}.{$this->appVersion}.min.js", ['defer' => true]);
            cfg::setCss("public/admin/{$this->appName}_assets/{$this->appVersion}/{$this->appName}.{$this->appVersion}.min.css");
        }


        $crmId = request::getGet('crm');
        $revisionId = request::getGet('revision');




        $view = new view(cfg::$data['mod_kat'] . "{$this->name}/tpl/root.php");
        $view->crmId = $crmId;
        $view->revisionId = $revisionId;
        $view->domain = offerhelper::domain();
        $view->display();
    }













    private function initializeTableOfContents()
    {
        $this->tableOfContents = [
            ['number' => 1, 'title' => 'Introduction', 'page' => 2],
            // 😺 pole items przetrzymuje informacje o subsekcjach, możliwe że nie będzie to potrzebne
            // 🙀 TODO: sprawdzić możliwości automatycznego generowanie spisu treści przez mpdf 
            ['number' => 2, 'title' => 'Technical Specifications', 'page' => 3, 'items' => [['number' => '2.1', 'title' => 'Item 1', 'page' => 3,]]],
            ['number' => 3, 'title' => 'Scope of Supply', 'page' => 4,],
            ['number' => 4, 'title' => 'Price and Commercial Terms', 'page' => 5,],
            ['number' => 5, 'title' => 'Delivery Terms', 'page' => 6,],
            ['number' => 6, 'title' => 'Warranty', 'page' => 7,],
            ['number' => 7, 'title' => 'Appendices', 'page' => 8,]
        ];
    }

    private function headerPDF($label = true)
    {
        $path = __DIR__ . '/images/banner.jpg';

        // Check if it's the first page
        if ($label) {
            // Other pages header - banner with info
            $style = "font-weight: bold";
            $topShift = $this->mTop + ($this->mTop / 2);

            $label = "<div style='width: 100%; text-align: right; font-size: 8px; margin-top: -{$topShift}mm; color: white;'>  
                        <span style='$style;'>Client:</span> {$this->client} <br>
                        <span style='$style;'>Project:</span> {$this->project} <br>
                        <span style='$style;'>Your reference:</span> {$this->yourReference} <br>
                        <span style='$style;'>Our reference:</span> {$this->ourReference} <br>
                    </div>";

            $html = "<div style='margin-left: -{$this->sheetGap}mm; margin-right: -{$this->sheetGap}mm;'>
                        <img src='{$path}' style='width: 100%; height: auto;'>
                    </div>
                    $label";
        } else {
            // First page header - only banner
            $html = "<div style='margin-left: -{$this->sheetGap}mm; margin-right: -{$this->sheetGap}mm;'>
                        <img src='{$path}' style='width: 100%; height: auto;'>
                    </div>";
        }

        $this->mpdf->SetHTMLHeader($html);
    }

    private function renderH2($number = 0, $text = "Heading 2 text")
    {
        $html = "<div class='subsection-container'>
                    <div class='colored-box'> Pos. $number.</div>
                    <div class='h2-wrapper'>
                        <h2>$text</h2>
                    </div>
                </div>";

        return $html;
    }

    private function renderList($items = [], $type = 'ul')
    {
        $wrapperClass = "width: 100%;";

        $html = "<div style='$wrapperClass'>";

        if ($type == 'ul') {
            $html .= "<ul>";

            foreach ($items as $item) {
                $html .= "<li>{$item}</li>";
            }

            $html .= "</ul>";
        } else {
            $html .= "<ol>";
            foreach ($items as $item) {
                $html .= "<li>{$item}</li>";
            }
            $html .= "</ol>";
        }

        $html .= '<div>';

        return $html;
    }
    private function renderObjectList($items = [], $type = 'ul')
    {
        $wrapperClass = "width: 100%;";

        $html = "<div style='$wrapperClass'>";

        if ($type == 'ul') {
            $html .= "<ul>";

            foreach ($items as $item) {
                if($item->visible == false) continue;

                if($item->type == "text"){
                    $html .= "<li>{$item->text}</li>";
                }

                if($item->type == "select"){
                    $html .= "<li>{$item->text}: {$item->value}</li>";
                }

                // $html .= "<li>{$item}</li>";
            }

            $html .= "</ul>";
        } else {
            $html .= "<ol>";
            foreach ($items as $item) {
                $html .= "<li>{$item}</li>";
            }
            $html .= "</ol>";
        }

        $html .= '<div>';

        return $html;
    }

    private function renderTableOfContents()
    {
        $html = '';
        foreach ($this->tableOfContents as $item) {
            $html .= "<div style='width: 100%; margin-bottom: 5px; '>";
            $html .= "<div style='width: 100%;'>
                        <div style='float: left; width: 85%;  font-weight: bold;'>{$item['title']}</div>
                        <div style='float: right; width: 15%; text-align: right; font-weight: bold;'>{$item['page']}</div>
                     </div>";

            if (isset($item['items'])) {
                foreach ($item['items'] as $subItem) {
                    $html .= "<div style='width: 100%; margin-top: 3px;'>
                        <div style='float: left; width: 74%; padding-left: 5%;'>{$subItem['title']}</div>
                        <div style='float: right; width: 15%; text-align: right; '>{$subItem['page']}</div>
                     </div>";
                }
            }
            $html .= "</div>";
        }

        return $html;
    }

    private function footerPDF()
    {
        $path = __DIR__ . '/images/footer.jpg';
        $html = "<div style='margin-left: -{$this->sheetGap}mm; margin-right: -{$this->sheetGap}mm;'>
                    <img src='{$path}' style='width: 100%; height: auto;'>
                 </div>";

        // Ustawienie stopki jako HTML
        $this->mpdf->SetHTMLFooter($html);
    }

    private function title($number, $text)
    {
        // 😼 tekst wyświetla się w tagu <h1>, zmiana w nadzieji, że uda się automatycznie generować spis treści
        $squareWidth = $this->sheetGap * 1.25;
        $padding = $this->letterGap;

        $html = "<div 
                    style='width: auto; 
                    font-size: 20px; 
                    font-weight: bold; 
                    border-bottom: 4px solid {$this->primaryColor};
                    margin-left: -{$this->sheetMarginLeft}mm;
                    margin-right: 0;
                    '>
                            <div 
                            style='float:left; 
                            color: white;
                            background-color: {$this->primaryColor};
                            padding-right: {$padding}mm; 
                            text-align: right; 
                            width: {$squareWidth}mm;'>
                            {$number}.
                            </div>

                            <div 
                            style='float:left; 
                            padding-left: {$padding}mm;
                            color: {$this->primaryColor};'>
                                <h1>{$text}</h1>
                            </div>
                </div>";

        return $html;
    }


    private function generateIntroduction()
    {
        return "<div style='font-size: 14px;'>
                    <p>Dear Customer,</p>
                    <p>Thank you for your interest in our products and services. 
                       This document presents our technical and commercial offer for {$this->project}.</p>
                    <!-- Dodaj więcej treści wprowadzenia -->
                </div>";
    }

    private function generatePricing()
    {
        $rows = "";
        $total = 0;
        foreach ($this->pricingTable as $item) {
            @$total += $item->total * 1;
            $rows .= "                
            <tr>
                <td >" . $item->number . "</td>
                <td style='text-align: left;'>" . $item->name . "</td>
                <td >" . $item->qty . "</td>
                <td >" . offerhelper::formatNumber($item->ppa) . "</td>
                <td >" . $item->currency . "</td>
                <td >" . offerhelper::formatNumber($item->total) . "</td>
                <td >" . $item->currency . "</td>      
            </tr>";
        }



        $html = "<div>";
        $html .= "<p>Incoterms conditions: <strong>{$this->pricingIncoterms}</strong></p>";
        $html .= "<p>";
        $html .= "<table class='pricing-table'>";
        $html .= "
                <tr>
                    <th></th>
                    <th style='width: 40%;'></th>
                    <th>Qty</th>
                    <th style='width: 20%;'>Price per article</th>
                    <th></th>
                    <th style='width: 20%;'>Total</th>
                    <th></th>
                </tr>";
        $html .= "                
                <tr>
                    <td></td>
                    <td></td>
                    <td  class='fw-bold'>1</td>
                    <td></td>
                    <td></td>
                    <td  class='fw-bold'>" . offerhelper::formatNumber($total) . "</td>
                    <td  class='fw-bold'>$this->pricingTotalCurrency</td>
                </tr>";
        $html .= $rows;


        $html .= "</table>
                    </p>
                </div>";

        return $html;
    }

    private function generateTechnicalContainer($item)
    {
        $domain = offerhelper::clearDomain();
        $publicUploadDirectory = 'public/img/offerimages/';
        $imgUrl = $domain. $publicUploadDirectory . $item->img;
        $imgTag = "<img style='height: 80mm;' src='$imgUrl' />";
        $imgStyle = "background-image: url($imgUrl);";

        $html = "<div class='technical-container'>
                    <div class='svg-container' style='$imgStyle'></div>
                    <div class='data-container'>
                        <table style='width: 100%;'>
                            <tr>
                                <td colspan='3'>
                                    <b>Operating and design data:</b>
                                </td>
                            </tr>
                            <tr>
                                <td>• Medium</td>
                                <td>{$item->medium}</td>
                                <td></td>
                            </tr>
                            <tr>
                                <td>• Density</td>
                                <td>{$item->density}</td>
                                <td>{$item->densityUnit}</td>
                            </tr>

                            <tr>
                                <td></td>
                                <td>
                                    <b><u>Operating</u></b>
                                </td>
                                <td>
                                    <b><u>Design</u></b>
                                </td>
                            </tr>
                            <tr>
                                <td>• Pressure</td>
                                <td>
                                    <table>
                                        <tr>
                                            <td>{$item->pressureOperatingValue}</td>
                                            <td>{$item->pressureOperatingUnit}</td>
                                        </tr>
                                    </table>
                                </td>
                                <td>
                                    <table>
                                        <tr>
                                            <td>{$item->pressureDesignValue}</td>
                                            <td>{$item->pressureDesignUnit}</td>
                                        </tr>
                                    </table>
                                </td>
                            </tr>
                            <tr>
                                <td>• Vaccum</td>
                                <td>
                                    <table>
                                        <tr>
                                            <td>{$item->vaccumOperatingValue}</td>
                                            <td>{$item->vaccumOperatingUnit}</td>
                                        </tr>
                                    </table>
                                </td>
                                <td>
                                    <table>
                                        <tr>
                                            <td>{$item->vaccumDesignValue}</td>
                                            <td>{$item->vaccumDesignUnit}</td>
                                        </tr>
                                    </table>
                                </td>
                            </tr>
                            <tr>
                                <td>• Temperature</td>
                                <td>
                                     <table>
                                        <tr>
                                            <td>{$item->temperatureOperatingValue}</td>
                                            <td>{$item->temperatureOperatingUnit}</td>
                                        </tr>
                                    </table>
                                </td>
                                <td>
                                      <table>
                                        <tr>
                                            <td>{$item->temperatureDesignValue}</td>
                                            <td>{$item->temperatureDesignUnit}</td>
                                        </tr>
                                    </table>
                                </td>
                            </tr>

                            <tr>
                                <td colspan='3'>
                                    <b>Dimensions:</b>
                                </td>
                            </tr>
                            <tr>
                                <td>• Total Volume:</td>
                                <td>{$item->totalVolumeOperatingValue}</td>
                                <td>{$item->totalVolumeOperatingUnit}</td>
                            </tr>
                            <tr>
                                <td>• Useful Volume:</td>
                                <td>{$item->usefulVolumeOperatingValue}</td>
                                <td>{$item->usefulVolumeOperatingUnit}</td>
                            </tr>
                            <tr>
                                <td>• Diameter (internal):</td>
                                <td>{$item->diameterOperatingValue}</td>
                                <td>{$item->diameterOperatingUnit}</td>
                            </tr>
                            <tr>
                                <td>• Cylindrical height:</td>
                                <td>{$item->cylindricalHeightOperatingValue}</td>
                                <td>{$item->cylindricalHeightOperatingUnit}</td>
                            </tr>
                            <tr>
                                <td>• Total height:</td>
                                <td>{$item->totalHeightOperatingValue}</td>
                                <td>{$item->totalHeightOperatingUnit}</td>
                            </tr>
                            <tr>
                                <td>• Weight (GRP only):</td>
                                <td>{$item->weightOperatingValue}</td>
                                <td>{$item->weightOperatingUnit}</td>
                            </tr>

                             <tr>
                                <td colspan='3'>
                                    <b>Dimensions:</b>
                                </td>
                            </tr>
                            <tr>
                                <td>• Corrosion barrier:</td>
                                <td colspan='2'>{$item->corrosionBarrierOperatingValue}</td>
                            </tr>
                            <tr>
                                <td>• Interlayer:</td>
                                <td colspan='2'>{$item->interlayerOperatingValue}</td>
                            </tr>
                            <tr>
                                <td>• Structural resin:</td>
                                <td colspan='2'>{$item->structuralResinOperatingValue}</td>
                            </tr>
                            <tr>
                                <td>• Topcoat:</td>
                                <td colspan='2'>{$item->topcoatOperatingValue}</td>
                            </tr>
                            <tr>
                                <td>• Additional:</td>
                                <td colspan='2'>{$item->additionalOperatingValue}</td>
                            </tr>
                        </table>
                    </div>
                </div>";

        return $html;
    }

    private function generateTechnicalSpecs()
    {
        $wrapperClass = "width: 100%;";

        $html =  "<div style='$wrapperClass'>";

        foreach ($this->technicalItems as $index => $item) {
            $html .= $this->renderH2($index + 1, $item->name);
            $html .= "<h3>General</h3>";
            $html .= $this->renderObjectList($item->general);

            $html .= "<div>";
            $html .= $this->generateTechnicalContainer($item);
            $html .= "</div>";

            $html .= "<br>
                    <h3>Execution description</h3>";
            $html .= "<p>{$item->executionDescription}</p>";

            $html .= "<br>
                    <h3>Equipment</h3>";
            $html .= $this->renderObjectList($item->equipment);


            $tableClass = "{$this->primaryTableBorder};";
            $tableThClass = "text-align: center; {$this->primaryTableBorder}; padding: 4px;";
            $tableTdClass = "text-align: center; {$this->primaryTableBorder}; padding: 4px;";

            $html .= "<pagebreak />

                <h3 style=''>
                    " . $item->notesLine1 . "
                </h3>";
            $html .= "<br>
                <table style='$tableClass'>";
            $html .= "
            <tr>
                <th style='$tableThClass; width: 10%;'><b>DN</b></th>
                <th style='$tableThClass; width: 40%;'><b>DESCRIPTION</b></th>
                <th style='$tableThClass; width: 10%;'><b>QUANTITY</b></th>
                <th style='$tableThClass; width: 40%;'><b>EQUIPMENT</b></th>
            </tr>
        ";


            foreach ($item->miscTable as $miscItem) {
                $html .= "
                <tr>
                <td style='$tableTdClass'>{$miscItem->dn}</td>
                <td style='$tableTdClass'>{$miscItem->description}</td>
                <td style='$tableTdClass'>{$miscItem->quantity}</td>
                <td style='$tableTdClass'>{$miscItem->equipment}</td></td>
            </tr>";
            }

            $html .= "</table>";

            $html .= "<br>
                    <p><b>{$item->notesLine2}</b></p>";

            $html .= "<br>
                    <h3 style=''>Exclusions</h3>";
            $html .= "<p>{$item->exclusions}</p>";
            if (count($this->technicalItems) != $index + 1) {
                $html .= "<pagebreak />";
            }
        }


        $html .= "</div>";

        return $html;
    }

    private function generateDocumentation()
    {
        $wrapperClass = "width: 100%;";
        $pTagClass = "margin: 0; padding-bottom: 5px;";

        $html =  "<div style='$wrapperClass'>";

        $html .= "<p style='$pTagClass'><strong>Language: </strong> {$this->documentationLanguage} </p>";
        $html .= "<p style='$pTagClass'><strong>Digital copies: </strong> {$this->documentationCopies} </p>";
        $html .= "<p style='$pTagClass'><strong>According: </strong> {$this->documentationAccording} </p>";
        $html .= "<p style='$pTagClass'><strong>Including</strong></p>";
        $html .= $this->renderList($this->documentationIncluding);
        $html .= "</div>";

        return $html;
    }

    private function generateConditions()
    {
        $wrapperClass = " width: 100%;";
        $h4Class = "border-bottom: 2px solid {$this->primaryColor}; width: 100%; margin-top: 20px; margin-bottom: 20px;";
        $headerColumnClass = 'float: left; width: 25%; font-weight: bold;';
        $contentColumnClass = 'float: left; width: 75%;';
        $pHeadClass = "margin-top: 25px; margin-bottom: 15px; font-weight: bold;";

        $html =  "<div style='$wrapperClass'>";
        $html .= "<h4 style='$h4Class'>Terms</h4>";

        foreach (['Invoicing', 'Payment', 'Prices', 'Warranty', 'Validity quotation'] as $row) {
            $html .= "<div style='margin-top: 10px;'>";
            $html .= "<div style='$headerColumnClass'>$row</div>";
            $html .= "<div style='$contentColumnClass'>";
            @$conditions = $this->conditionsTerms->$row;
            if ($conditions !== null) {
                foreach ($conditions as $item) {
                    $html .= "<div>$item</div>";
                }
            }
            $html .= "</div>";
            $html .= "</div>";
        }
        $html .= "<p style='margin-top: 20px; margin-bottom: 20px;'>{$this->conditionsNotes}</p>";
        $html .= "<h4 style='$h4Class'>Conditions</h4>";
        $html .= "<p style='$pHeadClass'>Sliding price clause</p>";
        $html .= "<p>{$this->conditionsSlidingPrice}</p>";
        $html .= "<p style='$pHeadClass'>Conditions of sale and service</p>";
        $html .= "<p>{$this->conditionsSaleAndService}</p>";
        $html .= "<p style='$pHeadClass'>Limitation of liability</p>";
        $html .= "<p>{$this->conditionsLimitationLiability}</p>";
        $html .= "<p style='$pHeadClass'>General notes</p>";
        $html .= "<p>{$this->conditionsGeneralNotes}</p>";

        $html .= "</div>";

        return $html;
    }

    private function generateGeneralRemarks()
    {
        $wrapperClass = " width: 100%;";
        $h4Class = "border-bottom: 2px solid {$this->primaryColor}; width: 100%;";
        $headerColumnClass = 'float: left; width: 25%; font-weight: bold;';
        $contentColumnClass = 'float: left; width: 75%;';

        $html =  "<div style='$wrapperClass'>";

        $html .= "<p>{$this->generalRemarks}</p>";

        $html .= "</div>";

        return $html;
    }

    private function generateTimeline()
    {
        $wrapperClass = " width: 100%;";

        $html =  "<div style='$wrapperClass'>";
        $html .=  "<p>To be discussed</p>";
        $html .=  "</div>";

        return $html;
    }

    private function generatePlaceholder()
    {
        $wrapperClass = " width: 100%;";

        $html =  "<div style='$wrapperClass'>";
        $html .=  "<div style='width: 100%; text-align: center; font-size: 30px; color: #808080'; font-weight: bold;>PLACEHOLDER</div>";
        $html .=  "</div>";

        return $html;
    }

    private function generateCover()
    {
        $date = date('d.m.Y');

        $topShift = $this->mTop * 2.5;
        $topShift = 0;
        $documentName = strtoupper($this->documentName);

        $html = "
        <div class='cover-section' style='padding-top: {$topShift}mm;'>
            <div class='cover-section__header fs-10pt' style='width: 100%; line-height: 1.4;'>
                <div class='head-left' 
                    style='float: left; width: 50%; border-left: 4px solid {$this->primaryColor};'>
                    <div style='padding-left: {$this->letterGap}mm;'>
                        <div style='font-weight: bold;'>Plasticon Poland S.A.</div>
                        <div>M. Skłodowskiej-Curie 59</div>
                        <div>87-100 Toruń, Poland</div>
                        <div>Tel: +48 56 6586100</div>
                        <div><EMAIL></div>
                    </div>
                </div>
                  
                <div class='head-right' 
                    style='float: left; text-align: right; border-right: 4px solid {$this->primaryColor};'>
                    <div style='padding-right: {$this->letterGap}mm;'>
                        <div style='font-weight: bold;'>Your contact for this project:</div>
                        <div>
                            <b>SALES: </b>{$this->salesName}, {$this->salesPhone}
                        </div>
                        <div>{$this->salesEmail}</div>
                        <div>
                            <b>TECHNICAL: </b>{$this->technicalName}, {$this->technicalPhone}  
                        </div>
                        <div>{$this->technicalEmail}</div>
                    </div>
                </div>
            </div>

            <br>

            <div class='cover-section__date fs-10pt' style='width: 100%; text-align: right;'>
                {$this->city}, {$this->date}
            </div>

            <br>

            <div class='cover-section__title fs-20pt fw-bold text-center' 
                style='width: 100%; color: {$this->primaryColor};'>
                $documentName
            </div>

            <div class='cover-section__ref fs-10pt text-center' style='width: 100%;'>
                <div>Your ref. {$this->yourReference}</div>
                <div>Our ref. {$this->ourReference}</div>
                <div class='fs-12pt fw-bold'>{$this->project}</div>
            </div>

            <br>

            <div class='cover-section__details fs-10pt' style='width: 100%; line-height: 1.4;'>
                <div class='details-left' 
                    style='float: left; width: 50%;'>
                    <div style=''>
                        <div class='fs-12pt'>{$this->client}</div>
                        <div>{$this->clientAddress}</div>
                        <div>{$this->clientPostCode}, {$this->clientCity}</div>
                        <div>{$this->clientCountry}</div>
                    </div>
                </div>

                <div class='cover-section__details-right' 
                    style='float: left; text-align: right;'>
                    <div style=''>
                        <div>For the attention of <b>{$this->clientName}</b></div>
                        <div>Mobile: {$this->clientPhone}</div>
                        <div>E-mail: {$this->clientEmail}</div>
                    </div>
                </div>
            </div>

            <br>

            <div class='cover-letter-container'>
                <div>
                    Dear Mr/Mrs {$this->clientName}, 
                </div>

                <br>

                <div class='content'>
                    {$this->coverLetter}
                </div>
                
                <br>

                <div>
                    Kind regards,
                    <p class='fs-10pt'>{$this->salesName}</p>
                </div>
            </div>
        </div>";

        return $html;
    }

    private function generateContent()
    {
        // Pobranie treści JSON z żądania
        $jsonData = file_get_contents("php://input");

        // Dekodowanie JSON do tablicy asocjacyjnej
        $data = json_decode($jsonData, true);

        // Pobranie wartości (jeśli istnieje)
        $range = isset($data['range']) ? $data['range'] : null;
        $pages = isset($data['pages']) ? $data['pages'] : null;


        $ids = array_column($this->sectionsView, 'id');

        $number = 0;

        // Generowanie każdej sekcji na nowej stronie
        foreach ($this->sections as $index => $section) {
            // if ($range && !in_array($section['id'], $pages)) {
            //     continue;
            // }

            if (!$this->sectionsView[array_search($section['id'], $ids)]->active) {
                continue;
            }
            $this->mpdf->AddPage();

           
            $title = $section['section'] ? $this->title($number, $section['title']) : ""; // 💩 sprawdz, czy tytuł sekcji powinien byc generowany.

            $html = "<div>
                        {$title}
                        <div style='margin-top: 6mm;'>
                            " . $section['content'] . "
                        </div>
                    </div>";
            $number++;

            $this->mpdf->WriteHTML($html);
        }
    }

    public function getgce()
    {
        $request = $this->getRequestBody();
        $payload = $request;

        // Sprawdzenie, czy $payload jest stringiem i próba jego dekodowania
        if (is_string($payload)) {
            $data = json_decode($payload);
            if (json_last_error() !== JSON_ERROR_NONE) {
                echo json_encode([
                    "success" => false,
                    "message" => "(offer.php - getgce 1) Invalid payload JSON structure",
                ]);
                return 0;
            }
        } else {
            $data = json_decode(json_encode($payload));
            if (json_last_error() !== JSON_ERROR_NONE) {

                echo json_encode([
                    "success" => false,
                    "message" => "(offer.php - getgce 2) Invalid payload JSON structure",
                ]);
                return 0;
            }
        }

        // Sprawdzenie czy JSON ma wymagane pola
        if (!isset($data->crmId)) {
            echo json_encode([
                "success" => false,
                "message" => "(offer.php - getgce 3)No CRM Id in payload",
            ]);
            return 0;
        }
        if (!isset($data->revisionId)) {
            echo json_encode([
                "success" => false,
                "message" => "(offer.php - 4)No CRM No in payload",
            ]);
            return 0;
        }
        
        
        offerhelper::getGceData($data->crmId);
    }

    public function getcrm()
    {
        $request = $this->getRequestBody();
        $payload = $request;

        // Sprawdzenie, czy $payload jest stringiem i próba jego dekodowania
        if (is_string($payload)) {
            $data = json_decode($payload);
            if (json_last_error() !== JSON_ERROR_NONE) {
                echo json_encode([
                    "success" => false,
                    "message" => "(offer.php - getcrm 1) Invalid payload JSON structure",
                ]);
                return 0;
            }
        } else {
            $data = json_decode(json_encode($payload));
            if (json_last_error() !== JSON_ERROR_NONE) {

                echo json_encode([
                    "success" => false,
                    "message" => "(offer.php - getcrm 2) Invalid payload JSON structure",
                ]);
                return 0;
            }
        }

        // Sprawdzenie czy JSON ma wymagane pola
        if (!isset($data->crmId)) {
            echo json_encode([
                "success" => false,
                "message" => "(offer.php - getcrm 3)No CRM Id in payload",
            ]);
            return 0;
        }
        
        
        
        $crmData = offerhelper::getCrmData($data->crmId);

        echo json_encode($crmData);
    }

    public function parseData($data)
    {


        $toDecodeFields = ['coverLetter', 'conditionsConditions', 'conditionsNotes', 'conditionsSlidingPrice', 'conditionsSaleAndService', 'conditionsLimitationLiability', 'conditionsGeneralNotes', 'generalRemarks'];
        $data = json_decode($data);
        foreach ($data->fields as $field => $value) {
            if (property_exists($this, $field)) {

                if (in_array($field, $toDecodeFields)) {
                    $this->$field = urldecode($value);
                } else {
                    $this->$field = $value;
                }
            } else {
                throw new Exception("Property $field does not exist");
            }
        }
        $this->sectionsView = $data->sections;
    }


    public function generate()
    {
        /*
            {
                "revisionId": 1,
                "zip" : 0, 
                "range" : 0,
                "pages" : []
            }
        */

        try {

            // 😼Walidacja zakomentowana tymczasowo w celu generacji pdf'u w oknie przeglądarki


            // Validate JSON input
            // $jsonData = file_get_contents("php://input");
            // if ($jsonData === false) {
            //     throw new Exception('Failed to read input data.');
            // }

            // $data = json_decode($jsonData, true);
            // if (json_last_error() !== JSON_ERROR_NONE) {
            //     throw new Exception('Invalid JSON format: ' . json_last_error_msg());
            // }




            $customID = isset($_GET['id']) ? $_GET['id'] : 5;
            $data = json_decode(json_encode([
                "revisionId" => $customID,
                "zip" => 0,
                "range" => 0,
                "pages" => []
            ]));

            // $zip = isset($data['zip']) ? $data['zip'] : false;
            $zip = isset($data->zip) ? $data->zip : false;

            // Initialize MPDF
            $this->mpdf->SetDisplayMode('fullpage');

            $revisionData = offerhelper::getRevisionById($data->revisionId);
            if (!$revisionData['success']) {
                echo json_encode([
                    "success" => false,
                    "message" => "(offer.php - 20)Invalid no data for revision id $data->revisionId",
                ]);
                return 0;
            }
            try {

                $this->parseData($revisionData['data']['revisions']['data']['offer_payload']);
                // $this->revData = $revisionData['data']['revisions']['data']['offer_payload'];
            } catch (Exception $e) {
                throw new Exception('Data parser Error: ' . $e->getMessage());
            }

            $this->sections = [
                [
                    'id' => 1,
                    'section' => false,
                    'title' => 'Cover Letter',
                    'content' => $this->generateCover()
                ],
                [
                    'id' => 2,
                    'section' => true,
                    'title' => 'Table of contents',
                    'content' => $this->renderTableOfContents()
                ],
                [
                    'id' => 3,
                    'section' => true,
                    'title' => 'Pricing',
                    'content' => $this->generatePricing()
                ],
                [
                    'id' => 4,
                    'section' => true,
                    'title' => 'Technical Specifications',
                    'content' => $this->generateTechnicalSpecs()
                ],
                [
                    'id' => 5,
                    'section' => true,
                    'title' => 'Documentation',
                    'content' => $this->generateDocumentation()
                ],
                [
                    'id' => 6,
                    'section' => true,
                    'title' => 'Timeline',
                    'content' => $this->generateTimeline()
                ],
                [
                    'id' => 7,
                    'section' => true,
                    'title' => 'Commercial conditions',
                    'content' => $this->generateConditions()
                ],
                [
                    'id' => 8,
                    'section' => true,
                    'title' => 'General remarks',
                    'content' => $this->generateGeneralRemarks()
                ],
                // [
                //     'id' => 9,
                //     'section' => true,
                //     'title' => 'Placeholder',
                //     'content' => $this->generatePlaceholder()
                // ],
                // Dodaj więcej sekcji według potrzeb
            ];



            // Generate header and footer
            try {
                $this->headerPDF();
                $this->footerPDF();
            } catch (Exception $e) {
                throw new Exception('Failed to generate header or footer: ' . $e->getMessage());
            }

            // Generate content
            try {
                $this->generateContent();
            } catch (Exception $e) {
                throw new Exception('Failed to generate content: ' . $e->getMessage());
            }

            if ($zip) {
                $this->generateZip();
            } else {
                try {
                    $this->mpdf->Output('offer.pdf', \Mpdf\Output\Destination::INLINE);
                } catch (\Mpdf\MpdfException $e) {
                    throw new Exception('Failed to generate PDF: ' . $e->getMessage());
                }
            }
        } catch (Exception $e) {
            $this->errorHandling($e);
        }
    }

    // FIlES
    private function insertFile($file, $crmId, $crmNumber, $revisionId, $type)
    {
        $author_id = user::get()->id();
        $author_name = user::get()->name();
        $author_date = date('Y-m-d H:i:s');

        $extension = pathinfo($file['name'], PATHINFO_EXTENSION);
        $hashedName = md5($file['name'] . time());

        $data = [
            "crm_id" => $crmId,
            "crm_number" => $crmNumber,
            "revision_id" => $revisionId,
            "type" => $type,
            "original_name" => $file['name'],
            "original_extension" => $extension,
            "original_size" => $file['size'],
            "hashed_name" => $hashedName,
            "author_id" => $author_id,
            "author_name" => $author_name,
            "author_date" => $author_date,

        ];

        $insertFileQuery = "
            INSERT INTO offerpdf.attachments
            (
                crm_id,
                crm_number,
                revision_id,
                type,
                original_name,
                original_extension,
                original_size,
                hashed_name,
                author_id,
                author_name,
                author_date
               
            )
            VALUES
            (
                '$crmId',
                '$crmNumber',
                '$revisionId',
                '$type',
                '{$file['name']}',
                '$extension',
                '{$file['size']}',
                '$hashedName',
                '$author_id',
                '$author_name',
                '$author_date'
               
            )";

        $insertFile = offerhelper::executeDatabaseQueryWithParams($insertFileQuery);
        if (!$insertFile['success']) {
            throw new Exception($insertFile['message']);
        }
        $insertFile['fileName'] = "$hashedName.$extension";

        return $insertFile;
    }

    public function removeFileById()
    {
        try {
            // Pobranie informacji o pliku
            $file = $this->getFileById();
            $id = isset($file['id']) ? $file['id'] : null;
            $id = txt::doSql($id);

            if (!$id) {
                throw new Exception("Nie znaleziono pliku o ID: $id");
            }

            // Przygotowanie zapytania z parametrem
            $sql = "SELECT hashed_name, original_extension FROM offer_attachments WHERE id = $id";

            $row = core::offer()->getRow($sql);

            if (!$row) {
                throw new Exception("Nie znaleziono pliku o ID: $id");
            }

            // Konstruowanie pełnej ścieżki pliku
            $fullFileName = $row['hashed_name'] . '.' . $row['original_extension'];
            $filePath = "attachments/" . $fullFileName; // Zakładam, że PATH_UPLOAD to stała ze ścieżką do katalogu uploadu

            // Usunięcie pliku z dysku
            if (file_exists($filePath)) {
                if (!unlink($filePath)) {
                    throw new Exception("Nie udało się usunąć pliku z dysku: $fullFileName");
                }
            }

            // Usunięcie wpisu z bazy danych
            $deleteSql = "DELETE FROM offer_attachments WHERE id = $id";
            $result = core::offer()->zap($deleteSql);

            if (!$result) {
                throw new Exception("Nie udało się usunąć wpisu z bazy danych dla pliku o ID: $id");
            }

            return true;
        } catch (PDOException $e) {
            $this->errorHandling($e);
        } catch (Exception $e) {
            $this->errorHandling($e);
        }
    }

    public function removeFileByHashedName($hashedName, $type = "image")
    {
        $securedUploadDirectory = 'offerattachments/';
        $publicUploadDirectory = 'public/img/offerimages/';

        $uploadDirectory = $securedUploadDirectory;

        if ($type == "image") {
            $uploadDirectory = $publicUploadDirectory;
        }

        try {
            $file = $this->getFileByHashedName($hashedName);
            if ($file) {

                $fullFileName = $file['hashed_name'] . '.' . $file['original_extension'];
                $filePath = $uploadDirectory . $fullFileName;

                // Usunięcie pliku z dysku
                if (file_exists($filePath)) {
                    if (!unlink($filePath)) {
                        throw new Exception("Nie udało się usunąć pliku z dysku: $fullFileName");
                    }
                }

                // Usunięcie wpisu z bazy danych
                $deleteSql = "DELETE FROM offerpdf.attachments WHERE id = {$file['id']}";
                $result = core::offer()->zap($deleteSql);

                if (!$result) {
                    throw new Exception("Nie udało się usunąć wpisu z bazy danych dla pliku o ID: {$file['id']}");
                }

                return [
                    'success' => true,
                    'message' => 'Plik został usuniety z dysku i z bazy danych'
                ];
            } else {
                throw new Exception("Nie znaleziono pliku o nazwie $hashedName");
            }
        } catch (Exception $e) {
            $this->errorHandling($e);
        }
    }

    public function upload()
    {
        if (!$_SERVER['REQUEST_METHOD'] === 'POST') {
            echo json_encode(['success' => false, 'message' => 'Invalid request method']);
            return 0;
        }

        if (!isset($_FILES['file'])) {
            echo json_encode(['success' => false, 'message' => 'No file uploaded']);
            return 0;
        }

        if (
            !isset($_POST['crmId']) ||
            !isset($_POST['crmNumber']) ||
            !isset($_POST['revisionId']) ||
            !isset($_POST['type'])
        ) {
            echo json_encode(['success' => false, 'message' => 'Metadata missing']);
            return 0;
        }

        $file = $_FILES['file'];
        $crmId = $_POST['crmId'];
        $crmNumber = $_POST['crmNumber'];
        $revisionId = $_POST['revisionId'];
        $type = $_POST['type'];
        $currentFile = isset($_POST['currentFile']) ? $_POST['currentFile'] : -1;



        $securedUploadDirectory = 'offerattachments/';
        $publicUploadDirectory = 'public/img/offerimages/';

        $uploadDirectory = $securedUploadDirectory;

        if ($type === 'image') {
            $uploadDirectory = $publicUploadDirectory;
        }

        // Sprawdź, czy folder istnieje, jeśli nie, stwórz go
        if (!is_dir($uploadDirectory)) {
            mkdir($uploadDirectory, 0777, true);
        }

        $instertedFileName = $this->insertFile($file, $crmId, $crmNumber, $revisionId, $type);
        if (!$instertedFileName['success']) {
            echo json_encode($instertedFileName);
            return 0;
        }
        $uploadFile = $uploadDirectory . $instertedFileName['fileName'];

        if (move_uploaded_file($file['tmp_name'], $uploadFile)) {
            // echo "<p>$type</p>";
            // echo "<p>$currentFile</p>";
            if ($type === 'image' && $currentFile != -1) {
                echo "<p>Try to delete</p>";
                $existingFile = $this->getFileByHashedName($currentFile);
                if ($existingFile) {

                    $deleted = $this->removeFileByHashedName($currentFile, $type);
                    if ($deleted['success']) {
                        echo json_encode(['success' => true, 'message' => 'File uploaded successfully, old file deleted', 'file' => $instertedFileName]);
                        return 1;
                    }
                }
            }
            echo json_encode(['success' => true, 'message' => 'File uploaded successfully', 'file' => $instertedFileName]);
            return 1;
        } else {
            echo json_encode(['success' => false, 'message' => 'File upload failed', 'file' => $instertedFileName]);
        }
    }

    private function generateZip()
    {
        $hashedNames = $this->getFilesByOfferId();

        try {
            if (!extension_loaded('zip')) {
                throw new Exception('ZIP extension is not installed on the server.');
            }

            // Set security headers
            header('X-Content-Type-Options: nosniff');
            header('X-Frame-Options: DENY');
            header('Content-Security-Policy: default-src \'none\'; frame-ancestors \'none\'');

            $zip = new ZipArchive();
            $zipName = 'package_' . date('Y-m-d_H-i-s') . '.zip';
            $uploadDirectory = 'attachments/';

            // Create new ZIP file
            $result = $zip->open($zipName, ZipArchive::CREATE);
            if ($result !== TRUE) {
                throw new Exception('Failed to create ZIP file. Error code: ' . $result);
            }

            // Generate and add PDF file
            try {
                $pdfResult = $this->mpdf->Output('temp_offer.pdf', \Mpdf\Output\Destination::FILE);
                if (!file_exists('temp_offer.pdf')) {
                    throw new Exception('Failed to generate PDF file.');
                }

                if (!$zip->addFile('temp_offer.pdf', 'offer.pdf')) {
                    throw new Exception('Failed to add PDF to ZIP file.');
                }
            } catch (\Mpdf\MpdfException $e) {
                throw new Exception('PDF generation failed: ' . $e->getMessage());
            }

            // Add files from attachments directory with additional security checks
            if (is_dir($uploadDirectory)) {
                $files = scandir($uploadDirectory);

                if ($files === false) {
                    throw new Exception('Failed to read attachments directory.');
                }

                foreach ($files as $file) {
                    if (!in_array($file, $hashedNames)) {
                        continue;
                    }

                    if ($file != '.' && $file != '..') {
                        $filePath = $uploadDirectory . $file;

                        // Additional security checks
                        if (strpos(realpath($filePath), realpath($uploadDirectory)) !== 0) {
                            dev::log('Security Warning: Attempted directory traversal - ' . $filePath);
                            continue;
                        }

                        if (is_file($filePath)) {
                            // Optional: Check file mime type
                            $finfo = finfo_open(FILEINFO_MIME_TYPE);
                            $mimeType = finfo_file($finfo, $filePath);

                            finfo_close($finfo);

                            // Add allowed mime types as needed
                            $allowedMimeTypes = [
                                'application/pdf',
                                'image/jpeg',
                                'image/png',
                                'application/msword',
                                'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
                                'application/vnd.ms-excel',
                                'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
                            ];

                            if (!in_array($mimeType, $allowedMimeTypes)) {
                                dev::log('Warning: Skipping file with unauthorized mime type: ' . $file);
                                continue;
                            }

                            if (!$zip->addFile($filePath, 'attachments/' . basename($file))) {
                                throw new Exception('Failed to add file to ZIP: ' . $file);
                            }
                        }
                    }
                }
            }

            // Close ZIP file
            if (!$zip->close()) {
                throw new Exception('Failed to close ZIP file.');
            }

            // Clean up temporary PDF
            if (file_exists('temp_offer.pdf')) {
                if (!unlink('temp_offer.pdf')) {
                    dev::log('Warning: Failed to remove temporary PDF file');
                }
            }

            // Verify ZIP file exists and is readable
            if (!file_exists($zipName) || !is_readable($zipName)) {
                throw new Exception('Generated ZIP file is not accessible.');
            }

            // Send ZIP file to browser with proper headers
            $fileSize = filesize($zipName);
            if ($fileSize === false) {
                throw new Exception('Failed to get ZIP file size.');
            }

            // Clear any existing output buffers
            while (ob_get_level()) {
                ob_end_clean();
            }

            // Set download headers
            header('Content-Type: application/zip');
            header('Content-Disposition: attachment; filename="' . basename($zipName) . '"');
            header('Content-Length: ' . $fileSize);
            header('Pragma: public');
            header('Cache-Control: must-revalidate, post-check=0, pre-check=0');
            header('Expires: 0');

            if (readfile($zipName) === false) {
                throw new Exception('Failed to send ZIP file to browser.');
            }

            // Clean up ZIP file
            if (!unlink($zipName)) {
                dev::log('Warning: Failed to remove ZIP file after sending');
            }
        } catch (Exception $e) {
            // Clean up any temporary files if they exist
            if (file_exists('temp_offer.pdf')) {
                unlink('temp_offer.pdf');
            }
            if (isset($zipName) && file_exists($zipName)) {
                unlink($zipName);
            }

            // Log the error
            dev::log('ZIP Generation Error: ' . $e->getMessage());

            $this->errorHandling($e);
        }
    }

    // Pobiera listę plików, które są powiązane z ofertą
    private function getFilesByOfferId()
    {
        $hashedNames = [];

        try {
            $offerId = $this->offerId;
            $sql = "SELECT * FROM offer_attachments WHERE offer_id = :offerId";

            $stmt = core::offer()->prepare($sql);
            $stmt->bindParam(':offerId', $offerId, PDO::PARAM_INT);
            $stmt->execute();

            $rows = $stmt->fetchAll(PDO::FETCH_ASSOC);

            foreach ($rows as $row) {
                $hashedName = $row['hashed_name'];
                $extension = $row['original_extension'];

                $hashedNames[] = "$hashedName.$extension";
            }
        } catch (PDOException $e) {
            // Obsługa błędów bazy danych
            $this->errorHandling($e);
        } catch (Exception $e) {
            // Obsługa innych wyjątków
            $this->errorHandling($e);
        }

        return $hashedNames;
    }

    public function getFileById()
    {
        try {
            // Validate JSON input
            $jsonData = file_get_contents("php://input");
            if ($jsonData === false) {
                throw new Exception('Failed to read input data.');
            }

            $data = json_decode($jsonData, true);
            if (json_last_error() !== JSON_ERROR_NONE) {
                throw new Exception('Invalid JSON format: ' . json_last_error_msg());
            }

            $id = isset($data['id']) ? $data['id'] : false;
            $sql = "SELECT * FROM offer_attachments WHERE id = $id";

            if (!$id) {
                throw new Exception('Missing "id" parameter.');
            }

            $row = core::offer()->getRow($sql);

            if (!$row) {
                throw new Exception('File not found.');
            }

            echo json_encode(['success' => true, 'message' => $row]);

            return $row;
        } catch (Exception $e) {
            $this->errorHandling($e);
        }
    }
    public function getFileByHashedName($hashedName)
    {
        try {
            $sql = "SELECT * FROM offerpdf.attachments WHERE hashed_name = '$hashedName'";
            $row = core::offer()->getRow($sql);

            if (!$row) {
                throw new Exception('File not found.');
            }

            echo json_encode(['success' => true, 'message' => $row]);
            return $row;
        } catch (Exception $e) {
            $this->errorHandling($e);
        }
    }
    // /files

    private function errorHandling($e)
    {
        // Send error response to client
        dev::log($e->getMessage());

        header('Content-Type: application/json');
        http_response_code(500);
        echo json_encode([
            'success' => false,
            'error' => 'Error: ' . $e->getMessage()
        ]);
    }
}

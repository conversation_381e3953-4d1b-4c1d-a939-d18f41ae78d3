<?php if(!defined('DS')){ exit(); }

$txt['rot_stat']="Statistics";
$txt['rot_stat_ogolne']="General information:";
$txt['rot_stat_name']="Name:";
$txt['rot_stat_ilosc']="Number:";
$txt['rot_stat_ilew']="Number of all elements";
$txt['rot_stat_sumaw']="Number of displays";
$txt['rot_stat_sumak']="Number of clicks";
$txt['rot_stat_types']="Types of files:";
$txt['rot_stat_link']="Outside links";
$txt['rot_stat_nw']="Most frequently displayed:";
$txt['rot_stat_nk']="Most frequently clicked-on:";
$txt['rot_stat_kw']="Clicks for displays:";
$txt['rot_types']="Types of banners:";
$txt['rot_types_name']="Category name";
$txt['rot_types_ilosc']="Number";
$txt['rot_types_typesp']="types of files:";
$txt['rot_types_sod']="width from:";
$txt['rot_types_px']="px";
$txt['rot_types_do']="to:";
$txt['rot_types_wod']="height from:";
$txt['rot_types_do']="to:";
$txt['rot_types_max']="max.:";
$txt['rot_types_kb']="kb)";
$txt['rot_types_wszystkie']="all banners";
$txt['rot_types_parameters']="parameters";
$txt['rot_types_wys']="show";
$txt['rot_types_pus']="Insert a blank or invalid values ​​will load the default settings for a category of banners. <br /> Some categories of safety reasons, may have a locked edit the displayed number.";

$txt['rot_arch_lista']="List of banners, ads (";
$txt['rot_arch_typ']="Type of banners:";
$txt['rot_arch_id']="id";
$txt['rot_arch_baner']="Banner";
$txt['rot_arch_aktywnosc']="Dates";
$txt['rot_arch_status']="Status";
$txt['rot_arch_jezyk']="Language";
$txt['rot_arch_prio']="Prior.";
$txt['rot_arch_udzial']="Share";
$txt['rot_arch_wys']="Displ.";
$txt['rot_arch_klik']="Clicks:";
$txt['rot_arch_od']="from:";
$txt['rot_arch_do']="to:";
$txt['rot_arch_astat']="statistics";
$txt['rot_arch_listakat']="list of categories of rotators/information";
$txt['rot_arch_szuk_status1']="active only";
$txt['rot_arch_szuk_status2']="inactive only";
$txt['rot_arch_szuk_status0']="--banner status--";
$txt['rot_arch_szuk_dostepne']="available only";
$txt['rot_arch_szuk_niedostepne']="unavailable only";
$txt['rot_arch_szuk_dostepnosc']="--display availability--";
$txt['rot_arch_lang']="--language version--";

$txt['rot_arch_w']="width:";
$txt['rot_arch_h']="height:";
$txt['rot_arch_px']="px";

$txt['rot_form_edycja']="Edit banner:";
$txt['rot_form_uzupelnij']="Fill in data";
$txt['rot_form_dodawanie']="Add banner:";
$txt['rot_form_dataod']="day of the start of the display";
$txt['rot_form_datado']="date due, show till day";
$txt['rot_form_typ']="type of banner/ad*:";
$txt['rot_form_typwyb']="--choose--";
$txt['rot_form_tytul']="title/name*:";
$txt['rot_form_linkg']="link on the banner:";
$txt['rot_form_okno']="window, in which the link opens";
$txt['rot_form_noweokno']="new window";
$txt['rot_form_tosamookno']="the same window";
$txt['rot_form_dotychczasowa']="existing graphic file:";
$txt['rot_form_grafika']="graphic file:*";
$txt['rot_form_wymiary']="size:";
$txt['rot_form_mozliwosc']="(manual modification only possible for SWF)";
$txt['rot_form_wys']="height (px)";
$txt['rot_form_szer']="width (px)";
$txt['rot_form_tlo']="background";
$txt['rot_form_tylko swf']="(only for SWF)";
$txt['rot_form_przezroczysty']="transparent";
$txt['rot_form_link']="code for the link/graphic file:";
$txt['rot_form_opis']="description, information:";
$txt['rot_form_czywys']="she displays be counted";
$txt['rot_form_wysw']="number of displays";
$txt['rot_form_wyslimit']="displays limit (0 = no limit)";
$txt['rot_form_czyklik']="Should clicks be counted";
$txt['rot_form_klik']="number of clicks";
$txt['rot_form_kliklimit']="clicks limit (0 = no limit)";
$txt['rot_form_brak']="--missing--";
$txt['rot_form_najwyzszy']=" - the lowest";
$txt['rot_form_najnizszy']=" - the highest";
$txt['rot_form_priorytet']="priority";
$txt['rot_form_udzial']="share";
$txt['rot_form_dowolna']="--any--";
$txt['rot_form_lang']="language version";
$txt['rot_form_lista']="list of banners/ads";
$txt['rot_form_reset']="Reset";

$txt['rot_form_artdo']="assigned to article";
$txt['rot_form_pp']="baner as file";
$txt['rot_form_pk']="or as code";

$txt['rot_form_zast']="alternative text/description:";
$txt['rot_form_section4']="availability:";
$txt['rot_form_section1']="flash file ()swf configuration:";
$txt['rot_form_section2']="counters and limits:";

$txt['rot_form_htyp']="Available types of banners depend on the www configuration created individually for every project";
$txt['rot_form_hadres']="WWW address which the given banner leads to - redirection to the link when clicked on the banner. Does not refer to SWF banners.";
$txt['rot_form_hgrafika']="Graphic file meeting the type and size requirements defined for the given type of banners";
$txt['rot_form_hwymiary']="System automatically reads the size of banners, it does not calibrate them with the exception of SWF. In case of SWF, the size read can be changed within limits set for the given type of banners.";
$txt['rot_form_htlo']="Choosing transparency causes the background colours to be ignored";
$txt['rot_form_hlink']="Link displaying the outside banner. When given, the banner, the link on the banner and other parameters of the banner are ignored. This type of banners does not allow to count clicks";
$txt['rot_form_hopis']="Only for administrative purposes, e.g. noting down information about the source of the banner or the advertisment contract";
$txt['rot_form_hwys']="Setting the display limit causes the banner to be displayed only as long as the display limit is greater than or equal to the number of displays";
$txt['rot_form_hklik']="Setting the clicks limit causes the banner to be displayed only as long as the clicks limit is greater than or equal to the number of clicks. Adding up clicks does not include banners opened by a link and SWF banners, for which the function of clicks couting is not available";
$txt['rot_form_hpriorytet']="Option used when displaying more than one banner at the same time. Banners are drawn according to their priority. Banners with the same priority are chosen in random order";
$txt['rot_form_hlangt']="Defines whether banner is to be displayed only for one language version or for all available versions";
$txt['rot_form_hudzial']="Option used when displaying more than one banner at the same time. anners are drawn according to their priority. Banners with the same priority are chosen in random order";

$txt['rot_zmiena_log']="rotator - parameter modification:";
$txt['rot_usuna_log']="rotator - removal";
$txt['rot_img_brak']="Incorrect type of file - rejected.";

$txt['rot_save_nname']="Empty name";
$txt['rot_save_ncat']="No cagtegory selected";
$txt['rot_achange']="change params";

?>
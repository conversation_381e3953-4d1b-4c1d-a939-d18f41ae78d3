Core.HiddenElements
TYPE: lookup
--DEFAULT--
array (
  'script' => true,
  'style' => true,
)
--DESCRIPTION--

<p>
  This directive is a lookup array of elements which should have their
  contents removed when they are not allowed by the HTML definition.
  For example, the contents of a <code>script</code> tag are not
  normally shown in a document, so if script tags are to be removed,
  their contents should be removed to. This is opposed to a <code>b</code>
  tag, which defines some presentational changes but does not hide its
  contents.
</p>
--# vim: et sw=4 sts=4

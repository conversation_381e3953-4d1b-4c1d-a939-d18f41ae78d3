<?php

if (!defined('DS')) {
    exit();
}

////////////////////////
//system configuration
///////////////////////
//empty config array
$cfg = array();

//windos or linux
$cfg['windows'] = getenv('windir');

//sys version
$cfg['sys_version'] = "7.2.1";

//project start date
$cfg['data_start'] = "2023-08-06";

//project type - default, shop, portal, app, bip
$cfg['project_type'] = "app";

//jezeli true to laduje dane z lokalnego komputera - patrz poniżej
$cfg['dev'] = false;

include_once(SYSROOT . "conf/db.php");

$cfg['intranet_url'] = "https://intranet.plasticon.app/";

$cfg['google_key'] = "";

//default error - page not exist action
$cfg['act_404'] = "";

$cfg['update_crm'] = true;

//ile na raz pobierac nowych projektow w synchronizacji
$cfg['limitsynchnew'] = 500;

//ile na raz aktualizowac projektow w synchronizacji
$cfg['limitsynchupdate'] = 250;

//php destination file
$cfg['index_file'] = "index.php";

//detect subfolder
$cfg['subfolder'] = str_replace("index.php", "", $_SERVER['SCRIPT_NAME']);

//prefix for sql tables
$mysql_przed = "gce_";
$mysql_przed_app = "plasticonapp_";
$mysql_przed_wtc = "plasticonwtc_";


//sql tables
$cfg['sql_tab']['cfg'] = $mysql_przed . "cfg";

$cfg['sql_tab']['users'] = $cfg['mysqlapp_baza'] . "." . $mysql_przed_app . "users";
$cfg['sql_tab']['users_session'] = $cfg['mysqlapp_baza'] . "." . $mysql_przed_app . "users_session";
$cfg['sql_tab']['logs'] = $cfg['mysqlapp_baza'] . "." . $mysql_przed_app . "logs";
$cfg['sql_tab']['bans'] = $cfg['mysqlapp_baza'] . "." . $mysql_przed_app . "bans";

$cfg['sql_tab']['offers_offers'] = $cfg['mysqlcrm_baza'] . ".offers";
$cfg['sql_tab']['offers_clients'] = $cfg['mysqlcrm_baza'] . ".clients";

$cfg['sql_tab']['users_users'] = $cfg['users_db'] . ".users";
$cfg['sql_tab']['users_crm'] = $cfg['users_db'] . ".crm";

$cfg['sql_tab']['suggestionbox'] = $mysql_przed . "suggestionbox";
$cfg['sql_tab']['suggestionbox_comments'] = $mysql_przed . "suggestionbox_comments";
$cfg['sql_tab']['suggestionbox_viewed'] = $mysql_przed . "suggestionbox_viewed";

$cfg['sql_tab']['config'] = $mysql_przed . "config";
$cfg['sql_tab']['sources'] = $mysql_przed . "sources";
$cfg['sql_tab']['segments'] = $mysql_przed . "segments";
$cfg['sql_tab']['units'] = $mysql_przed . "units";
$cfg['sql_tab']['articles'] = $mysql_przed . "articles";
$cfg['sql_tab']['competition'] = $mysql_przed . "competition";
$cfg['sql_tab']['exact'] = $mysql_przed . "exact";
$cfg['sql_tab']['exact_dictionary'] = $mysql_przed . "exact_dictionary";
$cfg['sql_tab']['iccms'] = $mysql_przed . "iccms";
$cfg['sql_tab']['config_search'] = $mysql_przed . "config_search";
$cfg['sql_tab']['search_history'] = $mysql_przed . "search_history";
$cfg['sql_tab']['offers'] = $mysql_przed . "offers";
$cfg['sql_tab']['inquirie'] = $mysql_przed . "inquirie";
$cfg['sql_tab']['versions'] = $mysql_przed . "versions"; //definicja tabeli w bazie
$cfg['sql_tab']['access_log'] = $mysql_przed . "access_log"; //definicja tabeli w bazie


$cfg['sql_tab']['risk_seeds'] = "risk_seeds";
$cfg['sql_tab']['risk_assesments'] = "risk_assesments";

//active system modules
$cfg['mod']['sys'] = true;
$cfg['mod']['users'] = true;
$cfg['mod']['uadmin'] = true;
$cfg['mod']['contact'] = false;
$cfg['mod']['cfgadmin'] = false;

$cfg['mod']['help'] = true;
$cfg['mod']['suggestionbox'] = true;

$cfg['mod']['config'] = true;
$cfg['mod']['gce'] = true;
$cfg['mod']['risk'] = true;
$cfg['mod']['cashflow'] = true;
$cfg['mod']['logs'] = true;
$cfg['mod']['guide'] = true;
$cfg['mod']['uploadform'] = true;
$cfg['mod']['products'] = true; //definicja modułu
$cfg['mod']['gceapi'] = true;
$cfg['mod']['offersapi'] = true;
$cfg['mod']['usersapi'] = true;
$cfg['mod']['offer'] = true;

//display cms branding - footer cms
$cfg['cms_branding'] = false;

//default timezone
$cfg['server_timezone'] = "Europe/Warsaw"; //define server timezone - server data save/generate timezone
$cfg['default_timezone'] = "Europe/Warsaw"; //define default user timezone
$cfg['user_timezone'] = ""; //user timezone - set by logged in users
$cfg['long_date'] = "Y-m-d H:i:s"; //long date format
$cfg['average_date'] = "Y-m-d H:i"; //middle date format
$cfg['short_date'] = "Y-m-d"; //short date format
$cfg['admin_long_date'] = "Y-m-d H:i:s"; //long date format
$cfg['admin_average_date'] = "Y-m-d H:i"; //middle date format
$cfg['admin_short_date'] = "Y-m-d"; //short date format
$cfg['change_timezone'] = false; //available change timezone

$cfg['sid_name'] = "sidtoken"; //sid token name
$cfg['session_name'] = "sesidtf"; //session SID name
//http path
if ($cfg['https'] || (!empty($_SERVER['HTTPS']) and filter_var($_SERVER['HTTPS'], FILTER_VALIDATE_BOOLEAN))) {
    $cfg['path'] = "https://" . $_SERVER['HTTP_HOST'] . $cfg['subfolder'];
    $cfg['fullpath'] = "https://" . $_SERVER['HTTP_HOST'] . $_SERVER['REQUEST_URI'];
} else {
    $cfg['path'] = "http://" . $_SERVER['HTTP_HOST'] . $cfg['subfolder'];
    $cfg['fullpath'] = "http://" . $_SERVER['HTTP_HOST'] . $_SERVER['REQUEST_URI'];
}

$cfg['maitenance'] = false; //maitenance mode
$cfg['maitenance_info'] = ""; //maitenance mode info for users
//www info
$cfg['admin_email'] = "<EMAIL>";
$cfg['contact_email'] = "<EMAIL>";
$cfg['contact_nadawca'] = "<EMAIL>";

$cfg['name_www'] = "GCE"; //sys name
$cfg['adres_www'] = $cfg['path'];

$cfg['autor'] = "";
$cfg['autor_email'] = "";
$cfg['autor_www'] = "";

$cfg['navtitle'] = "";
$cfg['title_seo'] = "Plasticon GCE"; //website title seo
$cfg['title_prefix'] = "Plasticon GCE"; //przedrostek do title generowanych automatycznie
$cfg['title_sufix'] = true;  //dla title czy dawac przedrostek na koncu
$cfg['description'] = "Plasticon GCE";
$cfg['keywords'] = "Plasticon GCE";
$cfg['robots'] = "noindex,nofollow";
$cfg['tabindex'] = false; //show tabindex info
$cfg['module_name'] = "";
$cfg['method_name'] = "";
$cfg['module_active'] = "";
$cfg['action_active'] = "";

//simple tag system in layout
$cfg['kodfooter'] = "";
$cfg['kodheader'] = "";
$cfg['kodstat'] = "";

//smtp email configuration
$cfg['contact_smtp_host'] = "";
$cfg['contact_smtp_user'] = "";
$cfg['contact_smtp_pass'] = "";
$cfg['contact_smtp_secure'] = ""; //ssl
$cfg['contact_smtp_port'] = "";

//email html template
$cfg['contact_szablon'] = "<html><head><meta http-equiv=\"Content-type\" content=\"text/html; charset=utf-8\"></head><body bgcolor=\"white\" style=\"font-size:12px; font-family: verdana;\"><div></div><TRESC></body></html>";
$cfg['contact_html'] = true;
$cfg['contact_grafiki'] = array(); //add images to each email

$cfg['social'] = false;
$cfg['social_likebutton'] = "";
$cfg['social_likebox'] = "";
$cfg['social_addto'] = "";
$cfg['social_facebook'] = "";

$cfg['og_image'] = "";
$cfg['og_title'] = "";
$cfg['og_description'] = "";

$cfg['breadcrumb'] = array();

$cfg['vat'] = 23;

$cfg['company_tab'] = array(
    'name' => "",
    'region' => "",
    'country' => "",
    'city' => "",
    'post_code' => "",
    'adress' => "",
    'phone' => "",
    'nip' => "",
    'bank_name' => "",
    'bank_account' => "",
    'payment_days' => 14,
);

//przyklady
//$cfg['contact_grafiki'][1]=array("plik"=>"tlo.gif","cid"=>"tlogif","typ"=>,"image/gif");
//$cfg['contact_grafiki'][2]=array("plik"=>"top.jpg","cid"=>"topjpg","typ"=>,"image/jpeg");
// uzywac np w formie <img src=\"cid:topjpg\" />
//graniczne domyslne  rozmiary uploadowanych grafik
$cfg['fotka_max'] = 750;  //max rozmiar, powyzej skalowane lub odrzucane
$cfg['fotka_min'] = 1;  //min rozmiar, ponizej odrzucane (z wyjatkiem SWF)
$cfg['size_max'] = 1536000; //150kb, nie dotyczy skalowanych JPG, PNG
$cfg['fotka_mini'] = 120; //rozmiar miniaturki
//prawidlowe rozszerzenia plikow graficznych: gif,jpg,png,swf,bmp: 1,2,3,4,13,6
$cfg['roz'][1] = "gif";
$cfg['roz'][2] = "jpg";
$cfg['roz'][3] = "png";
$cfg['roz'][4] = "swf";
$cfg['roz'][6] = "bmp";
$cfg['roz'][13] = "swf";

//language configuration
$cfg['przenies_lang'] = true;
$cfg['lang_name'] = "lang";
$cfg['lang_default'] = 2;

//https://pl.wikipedia.org/wiki/ISO_3166-1
//http://www.kurshtml.edu.pl/html/skroty_nazw_jezykow,jezyki.html
$cfg['tab_lang'] = array(
    1 => "pl",
    2 => "en",
    3 => "de",
    //	4=>"fr",
    //	5=>"it",
    //	6=>"ru",
    //	7=>"es",
);

//http://www.lingoes.net/en/translator/langcode.htm
$cfg['tab_langcodes'] = array(
    1 => "pl-PL",
    2 => "en-GB",
    3 => "de-DE",
    //	4=>"fr",
    //	5=>"it",
    //	6=>"ru",
    //	7=>"es",
);

//obsluga jezykow - panel admina
$cfg['lang2_default'] = 2;
$cfg['tab_lang2'] = array(
    1 => "pl",
    2 => "en",
    3 => "de",
);

//mozliwosc tworzenia przyjaznych linkow
$cfg['mod_rewrite'] = true;

//pasek uzywany w statystykach. [WIDTH] jest w nim zamieniane na dlugosc jaka ma miec w statystyce
//$cfg['pasek_stat']="<img src=\"public/img/sys/pasek.jpg\" height=\"7\" width=[WIDTH] alt=\"\" style=\"vertical-align:middle\" style=\"margin-right:3pt\" />";
$cfg['pasek_stat'] = "<div style=\"padding:3px; padding-left:0px;\"><div style=\"height:3px; width:[WIDTH]px; background-color:#d5550f; border:1px solid #000000; font-size:1px\"></div></div>";

//parametry nawigacji - ile stron na raz (*2+1)
$cfg['nawig_pages'] = 3;

//captcha configuration
$cfg['captcha_var'] = "cp"; //zmienna wpisany kod
$cfg['captcha_amount'] = 4;      //ile znakow

$cfg['antispam_var'] = "cr_url"; //zmienna antyspamowe
//CRSF security token
$cfg['crsf_token'] = true;

//html purifier
$cfg['htmlpurifier'] = true;

$cfg['l_tab'] = array("1", "A", "B", "C", "D", "E", "F", "G", "H", "I", "J", "K", "L", "M", "N", "O", "P", "Q", "R", "S", "T", "U", "W", "X", "Y", "Z");

//CMS editor folder
$cfg['editor'] = "public/editor/";

//upload main folder
$cfg['upload'] = "upload/";

//php libary folder
$cfg['lib'] = SYSROOT . "lib" . DS;

//language versions folder
$cfg['lang_kat'] = SYSROOT . "i18n" . DS;

//modules folder
$cfg['mod_kat'] = SYSROOT . "mod" . DS;

//templates folder
$cfg['tpl_kat'] = SYSROOT . "tpl" . DS;

//helpers folder
$cfg['helpers_kat'] = SYSROOT . "helpers" . DS;

//add full path to route url
$cfg['route_path'] = false;

//unique slugs in all modules
$cfg['unique_slug'] = true;

//default action in module
$cfg['action_default'] = "index";

//zend framework cache - documentation
//We choose a backend cache (for example 'File' or 'Sqlite'... empty value = no cache)
if (!empty($cfg['windows'])) {
    $cfg['cache'] = "";
} else {
    $cfg['cache'] = "File";
}

// array of options for the choosen frontend
$cfg['cache_frontend_options'] = array(
    'Core' => array(
        'lifetime' => 7200,
        'automatic_serialization' => true
    ),
    'Output' => array(),
    'Page' => array()
);

//array of options for the choosen backend
$cfg['cache_backend_options'] = array(
    'cache_dir' => DOCROOT . '/tmp/',
);

//default action,layout
switch ($cfg['project_type']) {

    case 'portal':
        $cfg['act_default'] = "users_start";
        $cfg['layout_default'] = "portal";
        break;

    case 'shop':
        $cfg['act_default'] = "shop_start";
        $cfg['layout_default'] = "shop";
        break;

    case 'app':
        $cfg['act_default'] = "users_panel";
        $cfg['layout_default'] = "admin";
        $cfg['social'] = false;
        break;

    case 'bip':
        $cfg['act_default'] = "art_start";
        $cfg['layout_default'] = "bip";
        break;

    default:
        $cfg['act_default'] = "calendar_index";
        $cfg['layout_default'] = "default";
}

/* routing tables - kohana framework */

$cfg['routes']['adminp'] = array(
    "route" => "(<langp>/)adminp",
    "regex" => array(),
    "defaults" => array(
        'langp' => "[a-zA-Z]{2}",
        'controller' => "users",
        'action' => "loginadmin",
    )
);

$cfg['routes']['xmlmap'] = array(
    "route" => "(<langp>/)sitemap.xml",
    "regex" => array(),
    "defaults" => array(
        'langp' => "[a-zA-Z]{2}",
        'controller' => "sys",
        'action' => "xmlmap",
    )
);

$cfg['routes']['elupload'] = array(
    "route" => "sys/elfinder/<type>",
    "regex" => array(
        'type' => "[a-zA-Z0-9]{3,}",
    ),
    "defaults" => array(
        'controller' => "sys",
        'action' => "elconnector",
        'type' => "file",
    )
);

$cfg['routes']['lang'] = array(
    "route" => "<langp>",
    "regex" => array(
        'langp' => "[a-zA-Z]{2}",
    ),
    "defaults" => array(
        'langp' => ""
    )
);

$cfg['routes']['public'] = array(
    "route" => "public/(<file>)",
    "regex" => array(
        'file' => ".*",
    ),
    "defaults" => array(
        'controller' => "sys",
        'action' => "err404",
    )
);

$cfg['routes']['upload'] = array(
    "route" => "upload/(<file>)",
    "regex" => array(
        'file' => ".*",
    ),
    "defaults" => array(
        'controller' => "sys",
        'action' => "err404",
    )
);

$cfg['routes']['artnew'] = array(
    "route" => "(<langp>/)<id_art>(/<page>)(/<idtf>)",
    "regex" => array(
        'langp' => "[a-zA-Z]{2}",
        'id_art' => "[0-9]{1,}",
        'page' => "[0-9]{1,}",
        'idtf' => "[a-zA-Z0-9\-_]{1,}",
    ),
    "defaults" => array(
        'controller' => "art",
        'action' => $cfg['action_default']
    )
);

$cfg['routes']['artnew2'] = array(
    "route" => "<idtf>,<id_art>(,s<page>)(,l<lang>).html",
    "regex" => array(
        'idtf' => "[a-zA-Z0-9\-_]{1,}",
        'id_art' => "[0-9]{1,}",
        'page' => "[0-9]{1,}",
        'lang' => "[0-9]{1,}",
    ),
    "defaults" => array(
        'controller' => "art"
    )
);

$cfg['routes']['arttags'] = array(
    "route" => "(<langp>/)tags(/<id_art>)/<tag>.html",
    "regex" => array(
        'langp' => "[a-zA-Z]{2}",
        'id_art' => "[0-9]{1,}",
    ),
    "defaults" => array(
        'tag' => "[-tag-]",
        'controller' => "art",
        'action' => "tags"
    )
);

$cfg['routes']['page'] = array(
    "route" => "(<langp>/)page/<idtf>",
    "regex" => array(
        'langp' => "[a-zA-Z]{2}",
        'idtf' => "[a-zA-Z0-9\-_\/]{1,}",
    ),
    "defaults" => array(
        'controller' => "art",
        'action' => $cfg['action_default']
    )
);

$cfg['routes']['page2'] = array(
    "route" => "(<langp>/)pages/<idtf2>",
    "regex" => array(
        'langp' => "[a-zA-Z]{2}",
        'idtf2' => "[a-zA-Z0-9\-_\/]{1,}",
    ),
    "defaults" => array(
        'controller' => "art",
        'action' => $cfg['action_default']
    )
);

$cfg['routes']['art'] = array(
    "route" => "<idtf>,<id_art>(,s<page>)(,l<lang>).html",
    "regex" => array(
        'idtf' => "[a-zA-Z0-9\-_]{1,}",
        'id_art' => "[0-9]{1,}",
        'page' => "[0-9]{1,}",
        'lang' => "[0-9]{1,}",
    ),
    "defaults" => array(
        'controller' => "art",
        'lang' => $cfg['lang_default'],
    )
);

$cfg['routes']['shopcat'] = array(
    "route" => "<idtf>,k<id_cat>(,s<page>)(,l<lang>).html",
    "regex" => array(
        'idtf' => "[a-zA-Z0-9\-_]{1,}",
        'id_cat' => "[0-9]{1,}",
        'lang' => "[0-9]{1,}",
        'page' => "[0-9]{1,}",
    ),
    "defaults" => array(
        'controller' => "shop",
        'lang' => $cfg['lang_default'],
    )
);

$cfg['routes']['shopcatnew'] = array(
    "route" => "(<langp>/)shop/<id_cat>(/<page>)(/<idtf>.html)",
    "regex" => array(
        'langp' => "[a-zA-Z]{2}",
        'id_cat' => "[0-9]{1,}",
        'page' => "[0-9]{1,}",
        'idtf' => "[a-zA-Z0-9\-_]{1,}",
    ),
    "defaults" => array(
        'controller' => "shop",
    )
);

$cfg['routes']['shopprodnew'] = array(
    "route" => "(<langp>/)product/<id_product>(/<page>)(/<idtf>.html)",
    "regex" => array(
        'langp' => "[a-zA-Z]{2}",
        'id_product' => "[0-9]{1,}",
        'page' => "[0-9]{1,}",
        'idtf' => "[a-zA-Z0-9\-_]{1,}",
    ),
    "defaults" => array(
        'controller' => "shop",
        'action' => "product",
    )
);

$cfg['routes']['users'] = array(
    "route" => "(<langp>/)users/<id_u>(/<idtf>.html)",
    "regex" => array(
        'langp' => "[a-zA-Z]{2}",
        'id_u' => "[0-9]{1,}",
        'idtf' => "[a-zA-Z0-9\-_]{1,}",
    ),
    "defaults" => array(
        'controller' => "users",
        'action' => "data",
    )
);

$cfg['routes']['userso'] = array(
    "route" => "(<langp>/)<controller>(/<action>)/u<id_u>(/<page>)",
    "regex" => array(
        'langp' => "[a-zA-Z]{2}",
        'controller' => "[a-zA-Z0-9]{3,}",
        'action' => "[a-zA-Z0-9]{1,}",
        'id_u' => "[0-9]{1,}",
        'page' => "[0-9]{1,}",
    ),
    "defaults" => array()
);

$cfg['routes']['forumf'] = array(
    "route" => "<idtf>,f<f>(,s<page>)(,l<lang>).html",
    "regex" => array(
        'idtf' => "[a-zA-Z0-9\-_]{1,}",
        'f' => "[0-9]{1,}",
        'page' => "[0-9]{1,}",
        'lang' => "[0-9]{1,}",
    ),
    "defaults" => array(
        'controller' => "forum",
        'lang' => $cfg['lang_default'],
    )
);

$cfg['routes']['forumt'] = array(
    "route" => "<idtf>,t<t>(,s<page>)(,l<lang>).html",
    "regex" => array(
        'idtf' => "[a-zA-Z0-9\-_]{1,}",
        't' => "[0-9]{1,}",
        'page' => "[0-9]{1,}",
        'lang' => "[0-9]{1,}",
    ),
    "defaults" => array(
        'controller' => "forum",
        'lang' => $cfg['lang_default'],
    )
);

$cfg['routes']['forumfn'] = array(
    "route" => "(<langp>/)forum/<f>(/<page>)(/<idtf>.html)",
    "regex" => array(
        'langp' => "[0-9]{1,}",
        'idtf' => "[a-zA-Z0-9\-_]{1,}",
        'f' => "[0-9]{1,}",
        'page' => "[0-9]{1,}",
    ),
    "defaults" => array(
        'controller' => "forum",
    )
);

$cfg['routes']['forumtn'] = array(
    "route" => "(<langp>/)forum/topic/<t>(/<page>)(/<idtf>.html)",
    "regex" => array(
        'langp' => "[0-9]{1,}",
        'idtf' => "[a-zA-Z0-9\-_]{1,}",
        't' => "[0-9]{1,}",
        'page' => "[0-9]{1,}",
    ),
    "defaults" => array(
        'controller' => "forum",
    )
);

$cfg['routes']['actions'] = array(
    "route" => "<act>,a(,s<page>)(,l<lang>).html",
    "regex" => array(
        'act' => "[a-zA-Z0-9\-_]{3,}",
        'lang' => "[0-9]{1,}",
        'page' => "[0-9]{1,}",
    ),
    "defaults" => array(
        'lang' => $cfg['lang_default'],
    )
);

$cfg['routes']['default'] = array(
    "route" => "(<langp>/)(<controller>(/<action>)(/<id>(/<idtf>.html)))",
    "regex" => array(
        'langp' => "[a-zA-Z]{2}",
        'controller' => "[a-zA-Z0-9]{3,}",
        'action' => "[a-zA-Z0-9]{1,}",
        'id' => "[0-9]{1,}",
        'idtf' => "[a-zA-Z0-9\-_]{1,}",
    ),
    "defaults" => array()
);

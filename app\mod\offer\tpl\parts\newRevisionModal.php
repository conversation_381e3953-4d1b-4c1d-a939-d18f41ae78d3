<div class="modal fade" id="addRevisionModal" tabindex="-1" aria-labelledby="addRevisionModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="addRevisionModalLabel">Add New Revision</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <form id="revisionForm">
                    <div class="mb-3">
                        <label for="revisionName" class="form-label">Revision Name</label>
                        <input type="text" class="form-control" id="revisionName" required>
                    </div>

                    <div class="mb-3">
                        <label for="revisionDescription" class="form-label">Description</label>
                        <textarea class="form-control" id="revisionDescription" rows="3"></textarea>
                    </div>
                    <div class="mb-3">
                        <label for="revisionLanguage" class="form-label">Revision Language</label>
                        <select class="form-select" id="revisionLanguage" required>
                            <option value="eng">English</option>
                            <option value="nld">Dutch</option>
                            <option value="ger">German</option>
                            <option value="pol">Polish</option>
                        </select>
                    </div>
                    <div class="mb-3">
                        <label for="revisionLocled" class="form-label">Lock Revision</label>
                        <select class="form-select" id="revisionLocked" required>
                            <option value="1">Locked Revision</option>
                            <option value="0">Open Revision</option>
                        </select>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                <button type="button" class="btn btn-primary" id="saveRevision">Save Revision</button>
            </div>
        </div>
    </div>
</div>

<script src="https://cdnjs.cloudflare.com/ajax/libs/axios/0.21.1/axios.min.js"></script>
<script>
    const sendDataToServer = async (url, data) => {
        try {
            const response = await axios.post(url, data);
            return response.data;
        } catch (error) {
            console.error('Error sending data:', error);
            throw error;
        }finally{
            window.location.reload();
        }
    };

    document.getElementById('saveRevision').addEventListener('click', function() {
        const form = document.getElementById('revisionForm');
        const inputs = form.querySelectorAll('input, select, textarea');
        let isValid = true;

        inputs.forEach(input => {
            if (input.hasAttribute('required') && !input.value) {
                isValid = false;
                input.classList.add('is-invalid');
            } else {
                input.classList.remove('is-invalid');
            }
        });

        if (isValid) {

            const crmId = "<?php echo $crm['id']; ?>";
            const crmNo = "<?php echo $crm['offerNo']; ?>";
            const name = document.getElementById('revisionName').value;
            const description = document.getElementById('revisionDescription').value;
            const locked = document.getElementById('revisionLocked').value;
            const language = document.getElementById('revisionLanguage').value;

            const data = {
                crmId: crmId,
                crmNo: crmNo,
                name: name,
                description: description,
                locked: locked,
                language: language
            };

            // SAVE AND LOAD NEW REVISIOM
            const url = "<?php echo offerhelper::domain(); ?>/new";
            const response = sendDataToServer(url, data);

            console.log('response', ' : ', response);


            const modal = bootstrap.Modal.getInstance(document.getElementById('addRevisionModal'));
            modal.hide();
            form.reset();
        }
    });
</script>
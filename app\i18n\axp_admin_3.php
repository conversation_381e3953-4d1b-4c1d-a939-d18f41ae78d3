<?php if(!defined('DS')){ exit(); }

$txt['axp_stat']="Statistik";
$txt['axp_stat_ogolne']="Allgemein:";
$txt['axp_stat_name']="Name:";
$txt['axp_stat_ilosc']="Anzahl:";
$txt['axp_stat_ilew']="Anzahl aller Elemente";
$txt['axp_stat_sumaw']="Summe der Abrufe";
$txt['axp_stat_sumak']="Summe der Klicks";
$txt['axp_stat_types']="Dateitypen:";
$txt['axp_stat_link']="Aussenverknuepfuengen";
$txt['axp_stat_nw']="Am haeufigsten angezeigt:";
$txt['axp_stat_nk']="Am haeufigsten angeklickt:";
$txt['axp_stat_kw']="Klicks zum Anzeigen:";
$txt['axp_types']="Typen der Banners:";
$txt['axp_types_name']="Name der Kategorie";
$txt['axp_types_ilosc']="Anzahl";
$txt['axp_types_typesp']="Dateitypen:";
$txt['axp_types_sod']="Breite ab:";
$txt['axp_types_px']="px";
$txt['axp_types_do']="bis:";
$txt['axp_types_wod']="Hoehe ab:";
$txt['axp_types_do']="bis:";
$txt['axp_types_max']="max.:";
$txt['axp_types_kb']="Kb)";
$txt['axp_types_wszystkie']="alle Banners";

$txt['axp_arch_lista']="Liste der Banners, Werbungen (";
$txt['axp_arch_typ']="Typen der Banners:";
$txt['axp_arch_id']="ID";
$txt['axp_arch_baner']="Banner";
$txt['axp_arch_aktywnosc']="Datum von bis";
$txt['axp_arch_status']="Status";
$txt['axp_arch_jezyk']="Sprache";
$txt['axp_arch_prio']="Priorität";
$txt['axp_arch_udzial']="Anteil";
$txt['axp_arch_wys']="Abrufe";
$txt['axp_arch_klik']="Klicks";
$txt['axp_arch_od']="von:";
$txt['axp_arch_do']="bis:";
$txt['axp_arch_astat']="Statistik";
$txt['axp_arch_listakat']="Liste der Kategorien der Wechsler/Informationen";
$txt['axp_arch_szuk_status1']="nur aktive";
$txt['axp_arch_szuk_status2']="nur inaktive";
$txt['axp_arch_szuk_status0']="--Status des Banners--";
$txt['axp_arch_szuk_dostepne']="nur verfügbar";
$txt['axp_arch_szuk_niedostepne']="nur nicht verfügbar";
$txt['axp_arch_szuk_dostepnosc']="--Zugang zum Abruf--";
$txt['axp_arch_lang']="--Sprachversion--";

$txt['axp_form_edycja']="Bearbeitung des Banners:";
$txt['axp_form_uzupelnij']="Daten ergaenzen";
$txt['axp_form_dodawanie']="Banner hinzufuegen:";
$txt['axp_form_dataod']="Zeige ab (Datum)";
$txt['axp_form_datado']="Gültigkeitsdatum, Zeige bis (Datum)";
$txt['axp_form_typ']="Typ des Baners/der Werbung*:";
$txt['axp_form_typwyb']="--auswaehlen--";
$txt['axp_form_tytul']="Titel/Name*:";
$txt['axp_form_linkg']="Verknuepfung auf dem Banner:";
$txt['axp_form_okno']="Fenster,in dem sich die Verknüpfung oeffnet";
$txt['axp_form_noweokno']="neues Fenster";
$txt['axp_form_tosamookno']="dasselbe Fenster";
$txt['axp_form_dotychczasowa']="bisherige Grafik:";
$txt['axp_form_grafika']="Grafik:*";
$txt['axp_form_wymiary']="Groesse:";
$txt['axp_form_mozliwosc']="(manuelle Aenderung nur fuer SWF moeglich)";
$txt['axp_form_wys']="Hoehe (px)";
$txt['axp_form_szer']="Breite (px)";
$txt['axp_form_tlo']="Hintergrund";
$txt['axp_form_tylko swf']="(nur fuer SWF)";
$txt['axp_form_przezroczysty']="transparent";
$txt['axp_form_link']="Kode zum Link/zur Grafik:";
$txt['axp_form_opis']="Beschreibung, Informationen:";
$txt['axp_form_czywys']="sollen Abrufe aufgezaehlt werden";
$txt['axp_form_wysw']="Anzahl der Abrufe";
$txt['axp_form_wyslimit']="Begrenzung der Abrufe (0 = keine Begrenzung)";
$txt['axp_form_czyklik']="sollen Klicks aufgezaehlt werden";
$txt['axp_form_klik']="Anzahl der Klicks";
$txt['axp_form_kliklimit']="Begrenzung der Klicks (0 = keine Begrenzung)";
$txt['axp_form_brak']="--keine--";
$txt['axp_form_najwyzszy']=" -  niedrigst";
$txt['axp_form_najnizszy']=" -  hoechst";
$txt['axp_form_priorytet']="Prioritaet";
$txt['axp_form_udzial']="Prozentanteil";
$txt['axp_form_dowolna']="--beliebig--";
$txt['axp_form_lang']="Sprachversion";
$txt['axp_form_lista']="Liste der Banners/Werbungen";
$txt['axp_form_reset']="Reset";

$txt['axp_form_artdo']="przypisany do artykułu/działu";
$txt['axp_form_pp']="baner w postaci pliku";
$txt['axp_form_pk']="lub w postaci kodu";

$txt['axp_form_zast']="tekst zastępczy/podpis:";
$txt['axp_form_section4']="dostępność:";
$txt['axp_form_section1']="konfiguracja pliku flash (swf):";
$txt['axp_form_section2']="liczniki i limity:";

$txt['axp_form_htyp']="Die zur Verfuegung stehenden Bannertypen hängen von der Konfiguration einer Website ab, die nach Wunsch erstellt wird";
$txt['axp_form_hadres']="Die Website-Adresse, an die das jeweilige Baner weiterleitet - Umleitung auf die Verknuepfung beim Anklicken des Banners. Betrifft keine SWF-Banners.";

$txt['axp_form_hgrafika']="Grafikdatei, die dem vorgegebenen Dateityp entspricht und die Groesse des Banners berücksichtigt";
$txt['axp_form_hwymiary']=" Das System erkennt die Groesse der Banners automatisch, skaliert sie nicht nur mit der Ausnahme der Dateien mt SWF-Format. Im Falle der SWF-Dateien kann die erkannte Groesse geandert werden (in von dem jeweiligen Bannertyp erlaubten Grenzen).";
$txt['axp_form_htlo']="Auswahl der Durchsichtigkeit bedeutet, dass die Hintergrundfarbe ignoriert wird";
$txt['axp_form_hlink']="Link, der den Aussenbanner anzeigt. Seine Angabe hat zur Folge, dass das eingebene Banner, eingegebene Veknuepfung auf dem Banner und andere Parameter des Banners ignoriert werden. Fuer diesen Typ von Banners besteht keine Moeglichkeit, die Klicks aufzuzaehlen";
$txt['axp_form_hopis']="Nur fuer administrative Anwendungen, z.B. Notieren von Informationen ueber die Quelle des Banners oder den Werbevertrag";
$txt['axp_form_hwys']="Angabe der Begrenzung der Abrufe bedeutet, dass Banner nur dann angezeigt werden kann, solange die Begrenzung hoeher als oder gleich ist wie die Anzahl der bisherigen Abrufe ist";
$txt['axp_form_hklik']="Angabe der Begrenzung der Klicks bedeutet, dass Banner nur dann angezeigt werden kann, solange die Begrenzung hoeher als oder gleich wie die Anzahl der bisherigen Klicks ist. Aufzaehlen der Klicks gilt nicht fuer die durch eine Verknuepfung eingefuehrte Banners sowie SWF Banners, die mit dieser Funktion nicht kompatibel sind";
$txt['axp_form_hpriorytet']="Diese Option wird beim Anzeigen mehrerer Banners auf einmal benutzt. Banners werden beginnend mit der hoechsten Prioritaet abgerufen. Banners mit der selben Prioritaet werden zufaellig ausgewaehlt";
$txt['axp_form_hlangt']="Es wird bezeichnet, ob ein Banner nur fuer eine Sprachversion angezeigt werden soll, oder fuer alle erhaeltichen";
$txt['axp_form_hudzial']="Diese Option wird  bei Anzeigen mehrerer Banners auf einmal benutzt. Banners werden beginnend mit der hoechsten Prioritaet genommen. Banners mit der selben Prioritaet werden zufaellig ausgewaehlt";

$txt['axp_zmiena_log']="Wechsler - Aenderung des Parameters:";
$txt['axp_usuna_log']="Wechsler - entfernen";
$txt['axp_img_brak']="Inkorrekter Dateityp - abgelehnt.";

?>
<?php if(!defined('DS')){ exit(); }

$txt['axp_stat']="Statistics";
$txt['axp_stat_ogolne']="General information:";
$txt['axp_stat_name']="Name:";
$txt['axp_stat_ilosc']="Number:";
$txt['axp_stat_ilew']="Number of all elements";
$txt['axp_stat_sumaw']="Number of displays";
$txt['axp_stat_sumak']="Number of clicks";
$txt['axp_stat_types']="Types of files:";
$txt['axp_stat_link']="Outside links";
$txt['axp_stat_nw']="Most frequently displayed:";
$txt['axp_stat_nk']="Most frequently clicked-on:";
$txt['axp_stat_kw']="Clicks for displays:";
$txt['axp_types']="Types of banners:";
$txt['axp_types_name']="Category name";
$txt['axp_types_ilosc']="Number";
$txt['axp_types_typesp']="types of files:";
$txt['axp_types_sod']="width from:";
$txt['axp_types_px']="px";
$txt['axp_types_do']="to:";
$txt['axp_types_wod']="height from:";
$txt['axp_types_do']="to:";
$txt['axp_types_max']="max.:";
$txt['axp_types_kb']="kb)";
$txt['axp_types_wszystkie']="all banners";
$txt['axp_types_parameters']="parameters";
$txt['axp_types_wys']="show";
$txt['axp_types_pus']="Insert a blank or invalid values ​​will load the default settings for a category of banners. <br /> Some categories of safety reasons, may have a locked edit the displayed number.";

$txt['axp_arch_lista']="List of banners, ads (";
$txt['axp_arch_typ']="Type of banners:";
$txt['axp_arch_id']="id";
$txt['axp_arch_baner']="Banner";
$txt['axp_arch_aktywnosc']="Dates";
$txt['axp_arch_status']="Status";
$txt['axp_arch_jezyk']="Language";
$txt['axp_arch_prio']="Prior.";
$txt['axp_arch_udzial']="Share";
$txt['axp_arch_wys']="Displ.";
$txt['axp_arch_klik']="Clicks:";
$txt['axp_arch_od']="from:";
$txt['axp_arch_do']="to:";
$txt['axp_arch_astat']="statistics";
$txt['axp_arch_listakat']="list of categories of rotators/information";
$txt['axp_arch_szuk_status1']="active only";
$txt['axp_arch_szuk_status2']="inactive only";
$txt['axp_arch_szuk_status0']="--banner status--";
$txt['axp_arch_szuk_dostepne']="available only";
$txt['axp_arch_szuk_niedostepne']="unavailable only";
$txt['axp_arch_szuk_dostepnosc']="--display availability--";
$txt['axp_arch_lang']="--language version--";

$txt['axp_arch_w']="width:";
$txt['axp_arch_h']="height:";
$txt['axp_arch_px']="px";

$txt['axp_form_edycja']="Edit banner:";
$txt['axp_form_uzupelnij']="Fill in data";
$txt['axp_form_dodawanie']="Add banner:";
$txt['axp_form_dataod']="day of the start of the display";
$txt['axp_form_datado']="date due, show till day";
$txt['axp_form_typ']="type of banner/ad*:";
$txt['axp_form_typwyb']="--choose--";
$txt['axp_form_tytul']="title/name*:";
$txt['axp_form_linkg']="link on the banner:";
$txt['axp_form_okno']="window, in which the link opens";
$txt['axp_form_noweokno']="new window";
$txt['axp_form_tosamookno']="the same window";
$txt['axp_form_dotychczasowa']="existing graphic file:";
$txt['axp_form_grafika']="graphic file:*";
$txt['axp_form_wymiary']="size:";
$txt['axp_form_mozliwosc']="(manual modification only possible for SWF)";
$txt['axp_form_wys']="height (px)";
$txt['axp_form_szer']="width (px)";
$txt['axp_form_tlo']="background";
$txt['axp_form_tylko swf']="(only for SWF)";
$txt['axp_form_przezroczysty']="transparent";
$txt['axp_form_link']="code for the link/graphic file:";
$txt['axp_form_opis']="description, information:";
$txt['axp_form_czywys']="she displays be counted";
$txt['axp_form_wysw']="number of displays";
$txt['axp_form_wyslimit']="displays limit (0 = no limit)";
$txt['axp_form_czyklik']="Should clicks be counted";
$txt['axp_form_klik']="number of clicks";
$txt['axp_form_kliklimit']="clicks limit (0 = no limit)";
$txt['axp_form_brak']="--missing--";
$txt['axp_form_najwyzszy']=" - the lowest";
$txt['axp_form_najnizszy']=" - the highest";
$txt['axp_form_priorytet']="priority";
$txt['axp_form_udzial']="share";
$txt['axp_form_dowolna']="--any--";
$txt['axp_form_lang']="language version";
$txt['axp_form_lista']="list of banners/ads";
$txt['axp_form_reset']="Reset";

$txt['axp_form_artdo']="assigned to article";
$txt['axp_form_pp']="baner as file";
$txt['axp_form_pk']="or as code";

$txt['axp_form_zast']="alternative text/description:";
$txt['axp_form_section4']="availability:";
$txt['axp_form_section1']="flash file ()swf configuration:";
$txt['axp_form_section2']="counters and limits:";

$txt['axp_form_htyp']="Available types of banners depend on the www configuration created individually for every project";
$txt['axp_form_hadres']="WWW address which the given banner leads to - redirection to the link when clicked on the banner. Does not refer to SWF banners.";
$txt['axp_form_hgrafika']="Graphic file meeting the type and size requirements defined for the given type of banners";
$txt['axp_form_hwymiary']="System automatically reads the size of banners, it does not calibrate them with the exception of SWF. In case of SWF, the size read can be changed within limits set for the given type of banners.";
$txt['axp_form_htlo']="Choosing transparency causes the background colours to be ignored";
$txt['axp_form_hlink']="Link displaying the outside banner. When given, the banner, the link on the banner and other parameters of the banner are ignored. This type of banners does not allow to count clicks";
$txt['axp_form_hopis']="Only for administrative purposes, e.g. noting down information about the source of the banner or the advertisment contract";
$txt['axp_form_hwys']="Setting the display limit causes the banner to be displayed only as long as the display limit is greater than or equal to the number of displays";
$txt['axp_form_hklik']="Setting the clicks limit causes the banner to be displayed only as long as the clicks limit is greater than or equal to the number of clicks. Adding up clicks does not include banners opened by a link and SWF banners, for which the function of clicks couting is not available";
$txt['axp_form_hpriorytet']="Option used when displaying more than one banner at the same time. Banners are drawn according to their priority. Banners with the same priority are chosen in random order";
$txt['axp_form_hlangt']="Defines whether banner is to be displayed only for one language version or for all available versions";
$txt['axp_form_hudzial']="Option used when displaying more than one banner at the same time. anners are drawn according to their priority. Banners with the same priority are chosen in random order";

$txt['axp_zmiena_log']="rotator - parameter modification:";
$txt['axp_usuna_log']="rotator - removal";
$txt['axp_img_brak']="Incorrect type of file - rejected.";

$txt['axp_save_nname']="Empty name";
$txt['axp_save_ncat']="No cagtegory selected";
$txt['axp_achange']="change params";

?>